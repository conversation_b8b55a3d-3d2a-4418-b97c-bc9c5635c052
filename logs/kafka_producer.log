2025-08-20 21:57:53,766 - kafka.conn - INFO - <BrokerConnection client_id=kafka-python-producer-1, node_id=bootstrap-0 host=localhost:9092 <connecting> [IPv6 ('::1', 9092, 0, 0)]>: connecting to localhost:9092 [('::1', 9092, 0, 0) IPv6]
2025-08-20 21:57:53,818 - kafka.conn - INFO - <BrokerConnection client_id=kafka-python-producer-1, node_id=bootstrap-0 host=localhost:9092 <checking_api_versions_recv> [IPv6 ('::1', 9092, 0, 0)]>: Broker version identified as 2.6
2025-08-20 21:57:53,818 - kafka.conn - INFO - <BrokerConnection client_id=kafka-python-producer-1, node_id=bootstrap-0 host=localhost:9092 <connected> [IPv6 ('::1', 9092, 0, 0)]>: Connection complete.
2025-08-20 21:57:53,843 - __main__ - INFO - �ɹ����ӵ�Kafka��Ⱥ: localhost:9092
2025-08-20 21:57:53,853 - __main__ - INFO - ======== ��ʼ�����ڻ�ϵͳ�������� ========
2025-08-20 21:57:53,856 - __main__ - INFO - �ɹ�����JSON�ļ�: ./data\single_leg_orders.json, ��¼��: 73
2025-08-20 21:57:53,856 - __main__ - INFO - ��ʼ���͵��ȶ������ݵ�Topic: singleleg_order_data_event
2025-08-20 21:57:53,965 - kafka.conn - INFO - <BrokerConnection client_id=kafka-python-producer-1, node_id=1 host=localhost:9092 <connecting> [IPv6 ('::1', 9092, 0, 0)]>: connecting to localhost:9092 [('::1', 9092, 0, 0) IPv6]
2025-08-20 21:57:53,965 - kafka.conn - INFO - <BrokerConnection client_id=kafka-python-producer-1, node_id=1 host=localhost:9092 <connected> [IPv6 ('::1', 9092, 0, 0)]>: Connection complete.
2025-08-20 21:57:53,965 - kafka.conn - INFO - <BrokerConnection client_id=kafka-python-producer-1, node_id=bootstrap-0 host=localhost:9092 <connected> [IPv6 ('::1', 9092, 0, 0)]>: Closing connection. 
2025-08-20 21:57:54,047 - kafka.cluster - WARNING - Topic singleleg_order_data_event is not available during auto-create initialization
2025-08-20 21:57:54,157 - kafka.cluster - WARNING - Topic singleleg_order_data_event is not available during auto-create initialization
2025-08-20 21:58:03,347 - __main__ - INFO - ���ȶ����������: �ɹ� 73/73
2025-08-20 21:58:03,347 - __main__ - INFO - ��ɷ��� single_leg_orders.json: 73 ����¼
2025-08-20 21:58:04,349 - __main__ - INFO - �ɹ�����JSON�ļ�: ./data\combination_orders.json, ��¼��: 15
2025-08-20 21:58:04,349 - __main__ - INFO - ��ʼ������϶������ݵ�Topic: cmb_order_data_event
2025-08-20 21:58:04,369 - kafka.cluster - WARNING - Topic cmb_order_data_event is not available during auto-create initialization
2025-08-20 21:58:06,327 - __main__ - INFO - ��϶����������: �ɹ� 15/15
2025-08-20 21:58:06,327 - __main__ - INFO - ��ɷ��� combination_orders.json: 15 ����¼
2025-08-20 21:58:07,328 - __main__ - WARNING - �����ļ�������: ./data\trades.json
2025-08-20 21:58:07,328 - __main__ - INFO - ======== ���ݷ�����ɣ��ܼ�: 88 ����¼ ========
2025-08-20 21:58:07,328 - kafka.producer.kafka - INFO - <KafkaProducer client_id=kafka-python-producer-1 transactional_id=None>: Closing the Kafka producer with 4294967.0 secs timeout.
2025-08-20 21:58:07,329 - kafka.conn - INFO - <BrokerConnection client_id=kafka-python-producer-1, node_id=1 host=localhost:9092 <connected> [IPv6 ('::1', 9092, 0, 0)]>: Closing connection. 
2025-08-20 21:58:07,329 - __main__ - INFO - Kafka�������ѹر�
