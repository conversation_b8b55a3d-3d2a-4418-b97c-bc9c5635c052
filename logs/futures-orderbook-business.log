22:08:22.226 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (1/4)#0
22:08:22.226 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (3/4)#0
22:08:22.226 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (2/4)#0
22:08:22.226 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (3/4)#0
22:08:22.226 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#0
22:08:22.226 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0
22:08:22.226 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0
22:08:22.226 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0
22:08:22.334 - SingleLegOrderDeserializer initialized
22:08:22.334 - ComboOrderDeserializer initialized
22:08:22.334 - SingleLegOrderDeserializer initialized
22:08:22.334 - ComboOrderDeserializer initialized
22:08:22.334 - ComboOrderDeserializer initialized
22:08:22.334 - SingleLegOrderDeserializer initialized
22:08:22.334 - SingleLegOrderDeserializer initialized
22:08:22.334 - ComboOrderDeserializer initialized
22:08:22.858 - 🔄 [JD2504-C-3200] 处理订单: 10000000 | 卖单 | 价格: 0.0 | 数量: 5
22:08:22.858 - 🔄 [CS2505-C-2750] 处理订单: 10000017 | 卖单 | 价格: 0.0 | 数量: 1
22:08:22.858 - 🔗 [EB2504-EB2505] 处理组合订单: 100257414 | 卖单 | 价格: 0.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:08:22.858 - 🔄 [SH2505] 处理订单: 10000021 | 卖单 | 价格: 0.0 | 数量: 1
22:08:22.872 - Order has invalid BS tag: SingleLegOrder{contractCde='SH2505', ordNbr='10000021', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698276773, expiryTimestamp=0}
22:08:22.872 - Order has invalid BS tag: SingleLegOrder{contractCde='CS2505-C-2750', ordNbr='10000017', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698275405, expiryTimestamp=0}
22:08:22.872 - Order has invalid BS tag: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000000', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698273857, expiryTimestamp=0}
22:08:22.872 - Combo order has invalid BS tag: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=13, timestamp=1755698284349}
22:08:22.872 - Invalid order received: SingleLegOrder{contractCde='SH2505', ordNbr='10000021', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698276773, expiryTimestamp=0}
22:08:22.872 - Invalid order received: SingleLegOrder{contractCde='CS2505-C-2750', ordNbr='10000017', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698275405, expiryTimestamp=0}
22:08:22.873 - Invalid order received: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000000', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698273857, expiryTimestamp=0}
22:08:22.873 - Invalid combo order received: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=13, timestamp=1755698284349}
22:08:22.873 - 🔄 [CS2505-C-2750] 处理订单: 10000017 | 卖单 | 价格: 0.0 | 数量: 0
22:08:22.873 - 🔄 [SH2505] 处理订单: 10000021 | 卖单 | 价格: 0.0 | 数量: 0
22:08:22.873 - 🔄 [JD2504-C-3200] 处理订单: 10000001 | 卖单 | 价格: 0.0 | 数量: 5
22:08:22.873 - 🔗 [EB2504-EB2505] 处理组合订单: 100257414 | 卖单 | 价格: 0.0 | 数量: 12 | 腿1: EB2504 | 腿2: EB2505
22:08:22.873 - Order has invalid BS tag: SingleLegOrder{contractCde='CS2505-C-2750', ordNbr='10000017', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698275529, expiryTimestamp=0}
22:08:22.873 - Order has invalid BS tag: SingleLegOrder{contractCde='SH2505', ordNbr='10000021', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698276895, expiryTimestamp=0}
22:08:22.873 - Order has invalid BS tag: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000001', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698274413, expiryTimestamp=0}
22:08:22.873 - Combo order has invalid BS tag: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=12, timestamp=1755698284591}
22:08:22.873 - Invalid order received: SingleLegOrder{contractCde='CS2505-C-2750', ordNbr='10000017', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698275529, expiryTimestamp=0}
22:08:22.874 - Invalid order received: SingleLegOrder{contractCde='SH2505', ordNbr='10000021', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698276895, expiryTimestamp=0}
22:08:22.874 - Invalid order received: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000001', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698274413, expiryTimestamp=0}
22:08:22.874 - Invalid combo order received: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=12, timestamp=1755698284591}
22:08:22.874 - 🔄 [CS2505-C-2750] 处理订单: 10000019 | 卖单 | 价格: 0.0 | 数量: 1
22:08:22.874 - 🔄 [JD2504-C-3200] 处理订单: 10000001 | 卖单 | 价格: 0.0 | 数量: 0
22:08:22.874 - 🔄 [SH2505] 处理订单: 10000023 | 卖单 | 价格: 0.0 | 数量: 1
22:08:22.874 - 🔗 [EB2504-EB2505] 处理组合订单: 100257414 | 卖单 | 价格: 0.0 | 数量: 11 | 腿1: EB2504 | 腿2: EB2505
22:08:22.874 - Order has invalid BS tag: SingleLegOrder{contractCde='CS2505-C-2750', ordNbr='10000019', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698275652, expiryTimestamp=0}
22:08:22.874 - Order has invalid BS tag: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000001', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698274535, expiryTimestamp=0}
22:08:22.874 - Order has invalid BS tag: SingleLegOrder{contractCde='SH2505', ordNbr='10000023', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698277018, expiryTimestamp=0}
22:08:22.874 - Combo order has invalid BS tag: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=11, timestamp=1755698284713}
22:08:22.874 - Invalid order received: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000001', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698274535, expiryTimestamp=0}
22:08:22.874 - Invalid order received: SingleLegOrder{contractCde='SH2505', ordNbr='10000023', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698277018, expiryTimestamp=0}
22:08:22.874 - Invalid combo order received: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=11, timestamp=1755698284713}
22:08:22.874 - Invalid order received: SingleLegOrder{contractCde='CS2505-C-2750', ordNbr='10000019', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698275652, expiryTimestamp=0}
22:08:22.875 - 🔄 [SH2505] 处理订单: 10000023 | 卖单 | 价格: 0.0 | 数量: 0
22:08:22.875 - 🔄 [JD2504-C-3200] 处理订单: 10000005 | 卖单 | 价格: 0.0 | 数量: 5
22:08:22.875 - 🔄 [CS2505-C-2750] 处理订单: 10000019 | 卖单 | 价格: 0.0 | 数量: 0
22:08:22.875 - 🔗 [EB2504-EB2505] 处理组合订单: 100257414 | 卖单 | 价格: 0.0 | 数量: 10 | 腿1: EB2504 | 腿2: EB2505
22:08:22.875 - Order has invalid BS tag: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000005', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698274659, expiryTimestamp=0}
22:08:22.875 - Order has invalid BS tag: SingleLegOrder{contractCde='SH2505', ordNbr='10000023', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698277144, expiryTimestamp=0}
22:08:22.875 - Order has invalid BS tag: SingleLegOrder{contractCde='CS2505-C-2750', ordNbr='10000019', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698275776, expiryTimestamp=0}
22:08:22.875 - Combo order has invalid BS tag: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=10, timestamp=1755698284839}
22:08:22.875 - Invalid order received: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000005', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698274659, expiryTimestamp=0}
22:08:22.875 - Invalid order received: SingleLegOrder{contractCde='SH2505', ordNbr='10000023', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698277144, expiryTimestamp=0}
22:08:22.875 - Invalid order received: SingleLegOrder{contractCde='CS2505-C-2750', ordNbr='10000019', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698275776, expiryTimestamp=0}
22:08:22.875 - Invalid combo order received: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=10, timestamp=1755698284839}
22:08:22.876 - 🔄 [JD2504-C-3200] 处理订单: 10000005 | 卖单 | 价格: 0.0 | 数量: 0
22:08:22.876 - 🔄 [EB2505] 处理订单: 100257097 | 卖单 | 价格: 0.0 | 数量: 10
22:08:22.876 - 🔗 [EB2504-EB2505] 处理组合订单: 100257414 | 卖单 | 价格: 0.0 | 数量: 9 | 腿1: EB2504 | 腿2: EB2505
22:08:22.876 - Order has invalid BS tag: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000005', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698274783, expiryTimestamp=0}
22:08:22.876 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257097', bSTag='null', trdPrc=0.0, rmnVol=10, timestamp=1755698276027, expiryTimestamp=0}
22:08:22.876 - Combo order has invalid BS tag: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=9, timestamp=1755698284962}
22:08:22.876 - Invalid order received: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000005', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698274783, expiryTimestamp=0}
22:08:22.876 - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257097', bSTag='null', trdPrc=0.0, rmnVol=10, timestamp=1755698276027, expiryTimestamp=0}
22:08:22.876 - Invalid combo order received: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=9, timestamp=1755698284962}
22:08:22.876 - 🔄 [JD2504-C-3200] 处理订单: 10000009 | 卖单 | 价格: 0.0 | 数量: 5
22:08:22.876 - 🔄 [EB2505] 处理订单: 100257007 | 卖单 | 价格: 0.0 | 数量: 10
22:08:22.876 - 🔗 [EB2504-EB2505] 处理组合订单: 100257414 | 卖单 | 价格: 0.0 | 数量: 8 | 腿1: EB2504 | 腿2: EB2505
22:08:22.876 - Order has invalid BS tag: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000009', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698274910, expiryTimestamp=0}
22:08:22.876 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257007', bSTag='null', trdPrc=0.0, rmnVol=10, timestamp=1755698276148, expiryTimestamp=0}
22:08:22.876 - Combo order has invalid BS tag: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=8, timestamp=1755698285086}
22:08:22.876 - Invalid order received: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000009', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698274910, expiryTimestamp=0}
22:08:22.876 - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257007', bSTag='null', trdPrc=0.0, rmnVol=10, timestamp=1755698276148, expiryTimestamp=0}
22:08:22.876 - Invalid combo order received: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=8, timestamp=1755698285086}
22:08:22.877 - 🔄 [JD2504-C-3200] 处理订单: 10000009 | 卖单 | 价格: 0.0 | 数量: 0
22:08:22.877 - 🔄 [EB2505] 处理订单: 100257007 | 卖单 | 价格: 0.0 | 数量: 0
22:08:22.877 - 🔗 [EB2504-EB2505] 处理组合订单: 100257414 | 卖单 | 价格: 0.0 | 数量: 7 | 腿1: EB2504 | 腿2: EB2505
22:08:22.877 - Order has invalid BS tag: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000009', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698275032, expiryTimestamp=0}
22:08:22.877 - Combo order has invalid BS tag: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=7, timestamp=1755698285210}
22:08:22.877 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257007', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698276273, expiryTimestamp=0}
22:08:22.877 - Invalid order received: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000009', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698275032, expiryTimestamp=0}
22:08:22.877 - Invalid combo order received: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=7, timestamp=1755698285210}
22:08:22.877 - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257007', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698276273, expiryTimestamp=0}
22:08:22.877 - 🔗 [EB2504-EB2505] 处理组合订单: 100257414 | 卖单 | 价格: 0.0 | 数量: 6 | 腿1: EB2504 | 腿2: EB2505
22:08:22.877 - 🔄 [JD2504-C-3200] 处理订单: 10000011 | 卖单 | 价格: 0.0 | 数量: 5
22:08:22.877 - Combo order has invalid BS tag: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=6, timestamp=1755698285335}
22:08:22.877 - 🔄 [EB2505] 处理订单: 100257004 | 卖单 | 价格: 0.0 | 数量: 10
22:08:22.877 - Order has invalid BS tag: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000011', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698275155, expiryTimestamp=0}
22:08:22.877 - Invalid combo order received: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=6, timestamp=1755698285335}
22:08:22.877 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257004', bSTag='null', trdPrc=0.0, rmnVol=10, timestamp=1755698276399, expiryTimestamp=0}
22:08:22.878 - Invalid order received: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000011', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698275155, expiryTimestamp=0}
22:08:22.878 - 🔗 [EB2504-EB2505] 处理组合订单: 100257414 | 卖单 | 价格: 0.0 | 数量: 4 | 腿1: EB2504 | 腿2: EB2505
22:08:22.878 - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257004', bSTag='null', trdPrc=0.0, rmnVol=10, timestamp=1755698276399, expiryTimestamp=0}
22:08:22.878 - 🔄 [JD2504-C-3200] 处理订单: 10000011 | 卖单 | 价格: 0.0 | 数量: 0
22:08:22.878 - Combo order has invalid BS tag: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=4, timestamp=1755698285459}
22:08:22.878 - 🔄 [EB2505] 处理订单: 100257004 | 卖单 | 价格: 0.0 | 数量: 0
22:08:22.878 - Order has invalid BS tag: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000011', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698275280, expiryTimestamp=0}
22:08:22.878 - Invalid combo order received: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=4, timestamp=1755698285459}
22:08:22.878 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257004', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698276524, expiryTimestamp=0}
22:08:22.878 - Invalid order received: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000011', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698275280, expiryTimestamp=0}
22:08:22.878 - 🔗 [EB2504-EB2505] 处理组合订单: 100257414 | 卖单 | 价格: 0.0 | 数量: 2 | 腿1: EB2504 | 腿2: EB2505
22:08:22.878 - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257004', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698276524, expiryTimestamp=0}
22:08:22.878 - Combo order has invalid BS tag: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698285583}
22:08:22.878 - 🔄 [JD2504-C-3200] 处理订单: 10000000 | 卖单 | 价格: 0.0 | 数量: 3
22:08:22.878 - 🔄 [EB2505] 处理订单: 100257098 | 卖单 | 价格: 0.0 | 数量: 3
22:08:22.879 - Invalid combo order received: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698285583}
22:08:22.879 - Order has invalid BS tag: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000000', bSTag='null', trdPrc=0.0, rmnVol=3, timestamp=1755698275901, expiryTimestamp=0}
22:08:22.879 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257098', bSTag='null', trdPrc=0.0, rmnVol=3, timestamp=1755698276648, expiryTimestamp=0}
22:08:22.879 - 🔗 [EB2504-EB2505] 处理组合订单: 100257414 | 卖单 | 价格: 0.0 | 数量: 1 | 腿1: EB2504 | 腿2: EB2505
22:08:22.879 - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257098', bSTag='null', trdPrc=0.0, rmnVol=3, timestamp=1755698276648, expiryTimestamp=0}
22:08:22.879 - Invalid order received: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000000', bSTag='null', trdPrc=0.0, rmnVol=3, timestamp=1755698275901, expiryTimestamp=0}
22:08:22.879 - Combo order has invalid BS tag: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698285707}
22:08:22.879 - 🔄 [EB2504] 处理订单: 100257000 | 卖单 | 价格: 0.0 | 数量: 1
22:08:22.879 - Invalid combo order received: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698285707}
22:08:22.879 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257000', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698277268, expiryTimestamp=0}
22:08:22.879 - 🔗 [EB2504-EB2505] 处理组合订单: 100257414 | 卖单 | 价格: 0.0 | 数量: 1 | 腿1: EB2504 | 腿2: EB2505
22:08:22.879 - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257000', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698277268, expiryTimestamp=0}
22:08:22.879 - Combo order has invalid BS tag: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698285831}
22:08:22.879 - 🔄 [EB2504] 处理订单: 100257001 | 卖单 | 价格: 0.0 | 数量: 1
22:08:22.879 - Invalid combo order received: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698285831}
22:08:22.880 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257001', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698277392, expiryTimestamp=0}
22:08:22.880 - 🔗 [EB2504-EB2505] 处理组合订单: 100257400 | 卖单 | 价格: 0.0 | 数量: 4 | 腿1: EB2504 | 腿2: EB2505
22:08:22.880 - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257001', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698277392, expiryTimestamp=0}
22:08:22.880 - Combo order has invalid BS tag: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257400', bSTag='null', trdPrc=0.0, rmnVol=4, timestamp=1755698285954}
22:08:22.880 - 🔄 [EB2504] 处理订单: 100257002 | 卖单 | 价格: 0.0 | 数量: 5
22:08:22.880 - Invalid combo order received: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257400', bSTag='null', trdPrc=0.0, rmnVol=4, timestamp=1755698285954}
22:08:22.880 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257002', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698277516, expiryTimestamp=0}
22:08:22.880 - 🔗 [EB2504-EB2505] 处理组合订单: 100257401 | 卖单 | 价格: 0.0 | 数量: 2 | 腿1: EB2504 | 腿2: EB2505
22:08:22.880 - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257002', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698277516, expiryTimestamp=0}
22:08:22.880 - Combo order has invalid BS tag: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257401', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698286078}
22:08:22.881 - 🔄 [EB2504] 处理订单: 100257003 | 卖单 | 价格: 0.0 | 数量: 1
22:08:22.881 - Invalid combo order received: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257401', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698286078}
22:08:22.881 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257003', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698277644, expiryTimestamp=0}
22:08:22.881 - 🔗 [EB2504-EB2505] 处理组合订单: 100257400 | 卖单 | 价格: 0.0 | 数量: 2 | 腿1: EB2504 | 腿2: EB2505
22:08:22.881 - Combo order has invalid BS tag: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257400', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698286202}
22:08:22.881 - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257003', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698277644, expiryTimestamp=0}
22:08:22.881 - Invalid combo order received: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257400', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698286202}
22:08:22.881 - 🔄 [EB2505] 处理订单: 100257009 | 卖单 | 价格: 0.0 | 数量: 1
22:08:22.881 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257009', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698277766, expiryTimestamp=0}
22:08:22.882 - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257009', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698277766, expiryTimestamp=0}
22:08:22.882 - 🔄 [EB2505] 处理订单: 100257005 | 卖单 | 价格: 0.0 | 数量: 1
22:08:22.882 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257005', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698277891, expiryTimestamp=0}
22:08:22.883 - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257005', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698277891, expiryTimestamp=0}
22:08:22.883 - 🔄 [EB2505] 处理订单: 100257006 | 卖单 | 价格: 0.0 | 数量: 5
22:08:22.883 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257006', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698278013, expiryTimestamp=0}
22:08:22.883 - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257006', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698278013, expiryTimestamp=0}
22:08:22.884 - 🔄 [EB2505] 处理订单: 100257008 | 卖单 | 价格: 0.0 | 数量: 1
22:08:22.884 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257008', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698278136, expiryTimestamp=0}
22:08:22.884 - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257008', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698278136, expiryTimestamp=0}
22:08:22.884 - 🔄 [EB2505] 处理订单: 100256966 | 卖单 | 价格: 0.0 | 数量: 9
22:08:22.885 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100256966', bSTag='null', trdPrc=0.0, rmnVol=9, timestamp=1755698278259, expiryTimestamp=0}
22:08:22.885 - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100256966', bSTag='null', trdPrc=0.0, rmnVol=9, timestamp=1755698278259, expiryTimestamp=0}
22:08:22.885 - 🔄 [EB2505] 处理订单: 100257085 | 卖单 | 价格: 0.0 | 数量: 8
22:08:22.885 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257085', bSTag='null', trdPrc=0.0, rmnVol=8, timestamp=1755698278383, expiryTimestamp=0}
22:08:22.886 - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257085', bSTag='null', trdPrc=0.0, rmnVol=8, timestamp=1755698278383, expiryTimestamp=0}
22:08:22.886 - 🔄 [EB2505] 处理订单: 100257085 | 卖单 | 价格: 0.0 | 数量: 0
22:08:22.886 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257085', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698278507, expiryTimestamp=0}
22:08:22.886 - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257085', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698278507, expiryTimestamp=0}
22:08:22.887 - 🔄 [EB2505] 处理订单: 100256966 | 卖单 | 价格: 0.0 | 数量: 1
22:08:22.887 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100256966', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698278630, expiryTimestamp=0}
22:08:22.887 - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100256966', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698278630, expiryTimestamp=0}
22:08:22.887 - 🔄 [EB2505] 处理订单: 100256966 | 卖单 | 价格: 0.0 | 数量: 1
22:08:22.887 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100256966', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698278754, expiryTimestamp=0}
22:08:22.887 - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100256966', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698278754, expiryTimestamp=0}
22:08:22.888 - 🔄 [EB2505] 处理订单: 100257259 | 卖单 | 价格: 0.0 | 数量: 1
22:08:22.888 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257259', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698278878, expiryTimestamp=0}
22:08:22.888 - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257259', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698278878, expiryTimestamp=0}
22:08:22.889 - 🔄 [EB2505] 处理订单: 100257259 | 卖单 | 价格: 0.0 | 数量: 0
22:08:22.889 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257259', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698279003, expiryTimestamp=0}
22:08:22.889 - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257259', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698279003, expiryTimestamp=0}
22:08:22.889 - 🔄 [EB2505] 处理订单: 100256966 | 卖单 | 价格: 0.0 | 数量: 0
22:08:22.889 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100256966', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698279127, expiryTimestamp=0}
22:08:22.890 - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100256966', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698279127, expiryTimestamp=0}
22:08:22.890 - 🔄 [EB2505] 处理订单: 100254367 | 卖单 | 价格: 0.0 | 数量: 1
22:08:22.890 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100254367', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698279251, expiryTimestamp=0}
22:08:22.890 - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100254367', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698279251, expiryTimestamp=0}
22:08:22.890 - 🔄 [EB2505] 处理订单: 100254367 | 卖单 | 价格: 0.0 | 数量: 0
22:08:22.890 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100254367', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698279375, expiryTimestamp=0}
22:08:22.890 - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100254367', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698279375, expiryTimestamp=0}
22:08:22.891 - 🔄 [EB2504] 处理订单: 100257479 | 卖单 | 价格: 0.0 | 数量: 1
22:08:22.891 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257479', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698279498, expiryTimestamp=0}
22:08:22.891 - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257479', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698279498, expiryTimestamp=0}
22:08:22.891 - 🔄 [EB2504] 处理订单: 100257479 | 卖单 | 价格: 0.0 | 数量: 0
22:08:22.891 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257479', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698279623, expiryTimestamp=0}
22:08:22.891 - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257479', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698279623, expiryTimestamp=0}
22:08:22.892 - 🔄 [EB2504] 处理订单: 100256192 | 卖单 | 价格: 0.0 | 数量: 1
22:08:22.892 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100256192', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698279747, expiryTimestamp=0}
22:08:22.892 - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100256192', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698279747, expiryTimestamp=0}
22:08:22.892 - 🔄 [EB2504] 处理订单: 100256192 | 卖单 | 价格: 0.0 | 数量: 0
22:08:22.892 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100256192', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698279871, expiryTimestamp=0}
22:08:22.892 - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100256192', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698279871, expiryTimestamp=0}
22:08:22.893 - 🔄 [EB2505] 处理订单: 100257507 | 卖单 | 价格: 0.0 | 数量: 1
22:08:22.893 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257507', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698279995, expiryTimestamp=0}
22:08:22.893 - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257507', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698279995, expiryTimestamp=0}
22:08:22.893 - 🔄 [EB2505] 处理订单: 100257507 | 卖单 | 价格: 0.0 | 数量: 0
22:08:22.893 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257507', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698280120, expiryTimestamp=0}
22:08:22.893 - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257507', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698280120, expiryTimestamp=0}
22:08:22.893 - 🔄 [EB2505] 处理订单: 100257466 | 卖单 | 价格: 0.0 | 数量: 1
22:08:22.893 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257466', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698280243, expiryTimestamp=0}
22:08:22.894 - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257466', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698280243, expiryTimestamp=0}
22:08:22.894 - 🔄 [EB2505] 处理订单: 100257466 | 卖单 | 价格: 0.0 | 数量: 0
22:08:22.894 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257466', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698280367, expiryTimestamp=0}
22:08:22.894 - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257466', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698280367, expiryTimestamp=0}
22:08:22.894 - 🔄 [EB2505] 处理订单: 100257500 | 卖单 | 价格: 0.0 | 数量: 5
22:08:22.894 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257500', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698280490, expiryTimestamp=0}
22:08:22.894 - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257500', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698280490, expiryTimestamp=0}
22:08:22.895 - 🔄 [EB2505] 处理订单: 100257500 | 卖单 | 价格: 0.0 | 数量: 3
22:08:22.895 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257500', bSTag='null', trdPrc=0.0, rmnVol=3, timestamp=1755698280614, expiryTimestamp=0}
22:08:22.895 - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257500', bSTag='null', trdPrc=0.0, rmnVol=3, timestamp=1755698280614, expiryTimestamp=0}
22:08:22.895 - 🔄 [EB2504] 处理订单: 100257560 | 卖单 | 价格: 0.0 | 数量: 5
22:08:22.895 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257560', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698280737, expiryTimestamp=0}
22:08:22.896 - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257560', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698280737, expiryTimestamp=0}
22:08:22.896 - 🔄 [EB2504] 处理订单: 100257560 | 卖单 | 价格: 0.0 | 数量: 2
22:08:22.896 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257560', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698280860, expiryTimestamp=0}
22:08:22.897 - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257560', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698280860, expiryTimestamp=0}
22:08:22.897 - 🔄 [EB2504] 处理订单: 100257560 | 卖单 | 价格: 0.0 | 数量: 0
22:08:22.897 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257560', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698280985, expiryTimestamp=0}
22:08:22.897 - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257560', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698280985, expiryTimestamp=0}
22:08:22.897 - 🔄 [EB2505] 处理订单: 100257500 | 卖单 | 价格: 0.0 | 数量: 1
22:08:22.898 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257500', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698281110, expiryTimestamp=0}
22:08:22.898 - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257500', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698281110, expiryTimestamp=0}
22:08:22.898 - 🔄 [EB2505] 处理订单: 100257500 | 卖单 | 价格: 0.0 | 数量: 0
22:08:22.898 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257500', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698281235, expiryTimestamp=0}
22:08:22.898 - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257500', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698281235, expiryTimestamp=0}
22:08:22.898 - 🔄 [EB2504] 处理订单: 100257608 | 卖单 | 价格: 0.0 | 数量: 1
22:08:22.898 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257608', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698281359, expiryTimestamp=0}
22:08:22.898 - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257608', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698281359, expiryTimestamp=0}
22:08:22.899 - 🔄 [EB2504] 处理订单: 100257608 | 卖单 | 价格: 0.0 | 数量: 0
22:08:22.899 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257608', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698281482, expiryTimestamp=0}
22:08:22.899 - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257608', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698281482, expiryTimestamp=0}
22:08:22.899 - 🔄 [EB2504] 处理订单: 100257648 | 卖单 | 价格: 0.0 | 数量: 2
22:08:22.900 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257648', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698281607, expiryTimestamp=0}
22:08:22.900 - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257648', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698281607, expiryTimestamp=0}
22:08:22.900 - 🔄 [EB2504] 处理订单: 100257648 | 卖单 | 价格: 0.0 | 数量: 1
22:08:22.900 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257648', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698281732, expiryTimestamp=0}
22:08:22.900 - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257648', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698281732, expiryTimestamp=0}
22:08:22.901 - 🔄 [EB2504] 处理订单: 100257648 | 卖单 | 价格: 0.0 | 数量: 0
22:08:22.901 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257648', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698281857, expiryTimestamp=0}
22:08:22.901 - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257648', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698281857, expiryTimestamp=0}
22:08:22.901 - 🔄 [EB2505] 处理订单: 100257736 | 卖单 | 价格: 0.0 | 数量: 2
22:08:22.901 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257736', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698281981, expiryTimestamp=0}
22:08:22.901 - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257736', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698281981, expiryTimestamp=0}
22:08:22.901 - 🔄 [EB2505] 处理订单: 100257736 | 卖单 | 价格: 0.0 | 数量: 2
22:08:22.901 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257736', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698282105, expiryTimestamp=0}
22:08:22.902 - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257736', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698282105, expiryTimestamp=0}
22:08:22.902 - 🔄 [EB2504] 处理订单: 100257414 | 卖单 | 价格: 0.0 | 数量: 10
22:08:22.902 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=10, timestamp=1755698282229, expiryTimestamp=0}
22:08:22.903 - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=10, timestamp=1755698282229, expiryTimestamp=0}
22:08:22.903 - 🔄 [EB2504] 处理订单: 100257414 | 卖单 | 价格: 0.0 | 数量: 0
22:08:22.903 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698282353, expiryTimestamp=0}
22:08:22.903 - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698282353, expiryTimestamp=0}
22:08:22.903 - 🔄 [EB2504] 处理订单: 100257342 | 卖单 | 价格: 0.0 | 数量: 10
22:08:22.903 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257342', bSTag='null', trdPrc=0.0, rmnVol=10, timestamp=1755698282477, expiryTimestamp=0}
22:08:22.903 - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257342', bSTag='null', trdPrc=0.0, rmnVol=10, timestamp=1755698282477, expiryTimestamp=0}
22:08:22.904 - 🔄 [EB2504] 处理订单: 100257342 | 卖单 | 价格: 0.0 | 数量: 0
22:08:22.904 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257342', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698282602, expiryTimestamp=0}
22:08:22.904 - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257342', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698282602, expiryTimestamp=0}
22:08:22.904 - 🔄 [EB2505] 处理订单: 100258082 | 卖单 | 价格: 0.0 | 数量: 2
22:08:22.904 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100258082', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698282727, expiryTimestamp=0}
22:08:22.904 - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100258082', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698282727, expiryTimestamp=0}
22:08:22.904 - 🔄 [EB2505] 处理订单: 100258082 | 卖单 | 价格: 0.0 | 数量: 2
22:08:22.904 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100258082', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698282851, expiryTimestamp=0}
22:08:22.905 - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100258082', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698282851, expiryTimestamp=0}
22:08:22.905 - 🔄 [EB2505] 处理订单: 100258081 | 卖单 | 价格: 0.0 | 数量: 2
22:08:22.905 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100258081', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698282976, expiryTimestamp=0}
22:08:22.905 - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100258081', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698282976, expiryTimestamp=0}
22:08:22.905 - 🔄 [EB2505] 处理订单: 100258083 | 卖单 | 价格: 0.0 | 数量: 2
22:08:22.905 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100258083', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698283100, expiryTimestamp=0}
22:08:22.905 - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100258083', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698283100, expiryTimestamp=0}
22:08:22.905 - 🔄 [EB2504] 处理订单: 100257349 | 卖单 | 价格: 0.0 | 数量: 1
22:08:22.905 - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257349', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698283224, expiryTimestamp=0}
22:08:22.906 - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257349', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698283224, expiryTimestamp=0}
22:17:16.450 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0
22:17:16.451 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (2/4)#0
22:17:16.450 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0
22:17:16.450 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0
22:17:16.450 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (3/4)#0
22:17:16.450 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#0
22:17:16.450 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (1/4)#0
22:17:16.450 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (3/4)#0
22:17:16.528 - SingleLegOrderDeserializer initialized
22:17:16.528 - ComboOrderDeserializer initialized
22:17:16.529 - ComboOrderDeserializer initialized
22:17:16.528 - SingleLegOrderDeserializer initialized
22:17:16.528 - ComboOrderDeserializer initialized
22:17:16.528 - SingleLegOrderDeserializer initialized
22:17:16.528 - SingleLegOrderDeserializer initialized
22:17:16.529 - ComboOrderDeserializer initialized
22:17:16.999 - ➕ [CS2505-C-2750] 新增/更新订单: 10000017 | 买单 | 价格: 13.0 | 数量: 1
22:17:16.999 - ➕ [JD2504-C-3200] 新增/更新订单: 10000000 | 买单 | 价格: 4.0 | 数量: 5
22:17:17.001 - 🔧 [CS2505-C-2750] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:17:17.001 - 🔧 [JD2504-C-3200] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:17:17.085 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:17:17.085 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:17:17.086 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:17:17.086 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:17:17.086 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:17:17.087 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:17:17.087 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:17:17.087 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:17:17.087 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:17:17.087 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:17:17.088 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:17:17.088 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 14 | 腿1: EB2504 | 腿2: EB2505
22:17:17.088 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257400 | 买单 | 价格: 80.0 | 数量: 4 | 腿1: EB2504 | 腿2: EB2505
22:17:17.088 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257401 | 买单 | 价格: 90.0 | 数量: 2 | 腿1: EB2504 | 腿2: EB2505
22:17:17.089 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257400 | 买单 | 价格: 85.0 | 数量: 2 | 腿1: EB2504 | 腿2: EB2505
22:17:18.130 - SingleLegOrderDeserializer initialized
22:17:18.131 - SingleLegOrderDeserializer initialized
22:17:18.133 - SingleLegOrderDeserializer initialized
22:17:18.139 - SingleLegOrderDeserializer initialized
22:17:18.139 - ComboOrderDeserializer initialized
22:17:18.141 - ComboOrderDeserializer initialized
22:17:18.143 - ComboOrderDeserializer initialized
22:17:18.149 - ComboOrderDeserializer initialized
22:17:18.159 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#1
22:17:18.160 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#1
22:17:18.163 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (3/4)#1
22:17:18.165 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#1
22:17:18.169 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (1/4)#1
22:17:18.171 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (2/4)#1
22:17:18.174 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (3/4)#1
22:17:18.175 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#1
22:17:18.239 - ➕ [JD2504-C-3200] 新增/更新订单: 10000000 | 买单 | 价格: 4.0 | 数量: 5
22:17:18.239 - ➕ [CS2505-C-2750] 新增/更新订单: 10000017 | 买单 | 价格: 13.0 | 数量: 1
22:17:18.239 - ➕ [SH2505] 新增/更新订单: 10000021 | 买单 | 价格: 2690.0 | 数量: 1
22:17:18.239 - 🔧 [JD2504-C-3200] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:17:18.239 - 🔧 [CS2505-C-2750] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:17:18.239 - 🔧 [SH2505] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:17:19.824 - SingleLegOrderDeserializer initialized
22:17:19.824 - SingleLegOrderDeserializer initialized
22:17:19.825 - SingleLegOrderDeserializer initialized
22:17:19.827 - SingleLegOrderDeserializer initialized
22:17:19.828 - ComboOrderDeserializer initialized
22:17:19.829 - ComboOrderDeserializer initialized
22:17:19.830 - ComboOrderDeserializer initialized
22:17:19.832 - ComboOrderDeserializer initialized
22:17:19.842 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#2
22:17:19.842 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (3/4)#2
22:17:19.845 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#2
22:17:19.850 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#2
22:17:19.852 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (1/4)#2
22:17:19.855 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (2/4)#2
22:17:19.857 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (3/4)#2
22:17:19.860 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#2
22:17:19.923 - ➕ [CS2505-C-2750] 新增/更新订单: 10000017 | 买单 | 价格: 13.0 | 数量: 1
22:17:19.923 - ➕ [JD2504-C-3200] 新增/更新订单: 10000000 | 买单 | 价格: 4.0 | 数量: 5
22:17:19.923 - ➕ [SH2505] 新增/更新订单: 10000021 | 买单 | 价格: 2690.0 | 数量: 1
22:17:19.923 - 🔧 [CS2505-C-2750] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:17:19.923 - 🔧 [JD2504-C-3200] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:17:19.923 - 🔧 [SH2505] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:17:22.128 - SingleLegOrderDeserializer initialized
22:17:22.129 - SingleLegOrderDeserializer initialized
22:17:22.130 - SingleLegOrderDeserializer initialized
22:17:22.130 - SingleLegOrderDeserializer initialized
22:17:22.132 - ComboOrderDeserializer initialized
22:17:22.133 - ComboOrderDeserializer initialized
22:17:22.135 - ComboOrderDeserializer initialized
22:17:22.136 - ComboOrderDeserializer initialized
22:17:22.145 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (3/4)#3
22:17:22.145 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#3
22:17:22.147 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#3
22:17:22.149 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#3
22:17:22.152 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (1/4)#3
22:17:22.153 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (2/4)#3
22:17:22.160 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#3
22:17:22.163 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (3/4)#3
22:17:22.230 - ➕ [CS2505-C-2750] 新增/更新订单: 10000017 | 买单 | 价格: 13.0 | 数量: 1
22:17:22.230 - ➕ [JD2504-C-3200] 新增/更新订单: 10000000 | 买单 | 价格: 4.0 | 数量: 5
22:17:22.230 - ➕ [SH2505] 新增/更新订单: 10000021 | 买单 | 价格: 2690.0 | 数量: 1
22:17:22.230 - 🔧 [CS2505-C-2750] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:17:22.230 - 🔧 [JD2504-C-3200] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:17:22.230 - 🔧 [SH2505] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:17:25.861 - SingleLegOrderDeserializer initialized
22:17:25.861 - SingleLegOrderDeserializer initialized
22:17:25.863 - SingleLegOrderDeserializer initialized
22:17:25.864 - SingleLegOrderDeserializer initialized
22:17:25.865 - ComboOrderDeserializer initialized
22:17:25.867 - ComboOrderDeserializer initialized
22:17:25.869 - ComboOrderDeserializer initialized
22:17:25.869 - ComboOrderDeserializer initialized
22:17:25.875 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#4
22:17:25.877 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#4
22:17:25.878 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#4
22:17:25.879 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (3/4)#4
22:17:25.885 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#4
22:17:25.886 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (2/4)#4
22:17:25.885 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (3/4)#4
22:17:25.891 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (1/4)#4
22:17:25.964 - ➕ [SH2505] 新增/更新订单: 10000021 | 买单 | 价格: 2690.0 | 数量: 1
22:17:25.964 - ➕ [CS2505-C-2750] 新增/更新订单: 10000017 | 买单 | 价格: 13.0 | 数量: 1
22:17:25.964 - ➕ [JD2504-C-3200] 新增/更新订单: 10000000 | 买单 | 价格: 4.0 | 数量: 5
22:17:25.965 - 🔧 [SH2505] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:17:25.965 - 🔧 [CS2505-C-2750] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:17:25.965 - 🔧 [JD2504-C-3200] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:17:31.487 - SingleLegOrderDeserializer initialized
22:17:31.487 - SingleLegOrderDeserializer initialized
22:17:31.487 - SingleLegOrderDeserializer initialized
22:17:31.489 - ComboOrderDeserializer initialized
22:17:31.490 - SingleLegOrderDeserializer initialized
22:17:31.491 - ComboOrderDeserializer initialized
22:17:31.492 - ComboOrderDeserializer initialized
22:17:31.493 - ComboOrderDeserializer initialized
22:17:31.499 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#5
22:17:31.500 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#5
22:17:31.501 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (3/4)#5
22:17:31.503 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#5
22:17:31.506 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (1/4)#5
22:17:31.507 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (3/4)#5
22:17:31.507 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (2/4)#5
22:17:31.511 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#5
22:17:31.590 - ➕ [CS2505-C-2750] 新增/更新订单: 10000017 | 买单 | 价格: 13.0 | 数量: 1
22:17:31.590 - ➕ [JD2504-C-3200] 新增/更新订单: 10000000 | 买单 | 价格: 4.0 | 数量: 5
22:17:31.590 - ➕ [SH2505] 新增/更新订单: 10000021 | 买单 | 价格: 2690.0 | 数量: 1
22:17:31.590 - 🔧 [CS2505-C-2750] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:17:31.590 - 🔧 [JD2504-C-3200] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:17:31.590 - 🔧 [SH2505] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:17:39.673 - SingleLegOrderDeserializer initialized
22:17:39.673 - SingleLegOrderDeserializer initialized
22:17:39.674 - SingleLegOrderDeserializer initialized
22:17:39.675 - SingleLegOrderDeserializer initialized
22:17:39.676 - ComboOrderDeserializer initialized
22:17:39.676 - ComboOrderDeserializer initialized
22:17:39.677 - ComboOrderDeserializer initialized
22:17:39.678 - ComboOrderDeserializer initialized
22:17:39.685 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (3/4)#6
22:17:39.685 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#6
22:17:39.685 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#6
22:17:39.688 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#6
22:17:39.688 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (1/4)#6
22:17:39.691 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (2/4)#6
22:17:39.693 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (3/4)#6
22:17:39.693 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#6
22:17:39.776 - ➕ [SH2505] 新增/更新订单: 10000021 | 买单 | 价格: 2690.0 | 数量: 1
22:17:39.777 - ➕ [JD2504-C-3200] 新增/更新订单: 10000000 | 买单 | 价格: 4.0 | 数量: 5
22:17:39.776 - ➕ [CS2505-C-2750] 新增/更新订单: 10000017 | 买单 | 价格: 13.0 | 数量: 1
22:17:39.777 - 🔧 [SH2505] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:17:39.777 - 🔧 [CS2505-C-2750] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:17:39.777 - 🔧 [JD2504-C-3200] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:17:50.539 - SingleLegOrderDeserializer initialized
22:17:50.539 - SingleLegOrderDeserializer initialized
22:17:50.540 - SingleLegOrderDeserializer initialized
22:17:50.541 - SingleLegOrderDeserializer initialized
22:17:50.543 - ComboOrderDeserializer initialized
22:17:50.543 - ComboOrderDeserializer initialized
22:17:50.545 - ComboOrderDeserializer initialized
22:17:50.546 - ComboOrderDeserializer initialized
22:17:50.551 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#7
22:17:50.551 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#7
22:17:50.554 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (3/4)#7
22:17:50.555 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#7
22:17:50.557 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (1/4)#7
22:17:50.559 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (2/4)#7
22:17:50.562 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (3/4)#7
22:17:50.564 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#7
22:17:50.641 - ➕ [CS2505-C-2750] 新增/更新订单: 10000017 | 买单 | 价格: 13.0 | 数量: 1
22:17:50.641 - ➕ [JD2504-C-3200] 新增/更新订单: 10000000 | 买单 | 价格: 4.0 | 数量: 5
22:17:50.641 - ➕ [SH2505] 新增/更新订单: 10000021 | 买单 | 价格: 2690.0 | 数量: 1
22:17:50.642 - 🔧 [CS2505-C-2750] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:17:50.642 - 🔧 [JD2504-C-3200] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:17:50.642 - 🔧 [SH2505] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:18:08.431 - SingleLegOrderDeserializer initialized
22:18:08.431 - SingleLegOrderDeserializer initialized
22:18:08.432 - SingleLegOrderDeserializer initialized
22:18:08.432 - SingleLegOrderDeserializer initialized
22:18:08.433 - ComboOrderDeserializer initialized
22:18:08.434 - ComboOrderDeserializer initialized
22:18:08.435 - ComboOrderDeserializer initialized
22:18:08.435 - ComboOrderDeserializer initialized
22:18:08.441 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#8
22:18:08.442 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#8
22:18:08.443 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (3/4)#8
22:18:08.445 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#8
22:18:08.447 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (1/4)#8
22:18:08.453 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (3/4)#8
22:18:08.453 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (2/4)#8
22:18:08.455 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#8
22:18:08.534 - ➕ [JD2504-C-3200] 新增/更新订单: 10000000 | 买单 | 价格: 4.0 | 数量: 5
22:18:08.534 - ➕ [CS2505-C-2750] 新增/更新订单: 10000017 | 买单 | 价格: 13.0 | 数量: 1
22:18:08.534 - ➕ [SH2505] 新增/更新订单: 10000021 | 买单 | 价格: 2690.0 | 数量: 1
22:18:08.534 - 🔧 [JD2504-C-3200] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:18:08.534 - 🔧 [CS2505-C-2750] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:18:08.535 - 🔧 [SH2505] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:18:08.536 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:18:08.536 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:18:08.536 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:18:08.536 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:18:08.536 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:18:08.537 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:18:08.537 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:18:08.537 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:18:08.537 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:18:08.537 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:18:08.537 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:18:08.537 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 14 | 腿1: EB2504 | 腿2: EB2505
22:18:08.538 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257400 | 买单 | 价格: 80.0 | 数量: 4 | 腿1: EB2504 | 腿2: EB2505
22:18:08.538 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257401 | 买单 | 价格: 90.0 | 数量: 2 | 腿1: EB2504 | 腿2: EB2505
22:18:08.538 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257400 | 买单 | 价格: 85.0 | 数量: 2 | 腿1: EB2504 | 腿2: EB2505
22:18:33.297 - SingleLegOrderDeserializer initialized
22:18:33.297 - SingleLegOrderDeserializer initialized
22:18:33.297 - SingleLegOrderDeserializer initialized
22:18:33.298 - ComboOrderDeserializer initialized
22:18:33.299 - ComboOrderDeserializer initialized
22:18:33.300 - SingleLegOrderDeserializer initialized
22:18:33.300 - ComboOrderDeserializer initialized
22:18:33.301 - ComboOrderDeserializer initialized
22:18:33.306 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#9
22:18:33.308 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (3/4)#9
22:18:33.310 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#9
22:18:33.312 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#9
22:18:33.312 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (1/4)#9
22:18:33.314 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#9
22:18:33.316 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (2/4)#9
22:18:33.318 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (3/4)#9
22:18:33.398 - ➕ [JD2504-C-3200] 新增/更新订单: 10000000 | 买单 | 价格: 4.0 | 数量: 5
22:18:33.398 - ➕ [SH2505] 新增/更新订单: 10000021 | 买单 | 价格: 2690.0 | 数量: 1
22:18:33.399 - 🔧 [JD2504-C-3200] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:18:33.398 - ➕ [CS2505-C-2750] 新增/更新订单: 10000017 | 买单 | 价格: 13.0 | 数量: 1
22:18:33.399 - 🔧 [SH2505] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:18:33.399 - 🔧 [CS2505-C-2750] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:19:12.662 - SingleLegOrderDeserializer initialized
22:19:12.662 - SingleLegOrderDeserializer initialized
22:19:12.663 - SingleLegOrderDeserializer initialized
22:19:12.664 - SingleLegOrderDeserializer initialized
22:19:12.664 - ComboOrderDeserializer initialized
22:19:12.665 - ComboOrderDeserializer initialized
22:19:12.666 - ComboOrderDeserializer initialized
22:19:12.667 - ComboOrderDeserializer initialized
22:19:12.673 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#10
22:19:12.674 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (3/4)#10
22:19:12.675 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#10
22:19:12.678 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#10
22:19:12.682 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (1/4)#10
22:19:12.684 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (3/4)#10
22:19:12.685 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (2/4)#10
22:19:12.685 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#10
22:19:12.766 - ➕ [SH2505] 新增/更新订单: 10000021 | 买单 | 价格: 2690.0 | 数量: 1
22:19:12.766 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:19:12.766 - ➕ [CS2505-C-2750] 新增/更新订单: 10000017 | 买单 | 价格: 13.0 | 数量: 1
22:19:12.766 - ➕ [JD2504-C-3200] 新增/更新订单: 10000000 | 买单 | 价格: 4.0 | 数量: 5
22:19:12.766 - 🔧 [CS2505-C-2750] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:19:12.766 - 🔧 [SH2505] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:19:12.766 - 🔧 [JD2504-C-3200] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:19:12.766 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:19:12.767 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:19:12.767 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:19:12.767 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:19:12.767 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:19:12.768 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:19:12.768 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:19:12.768 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:19:12.768 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:19:12.768 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:19:12.768 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 14 | 腿1: EB2504 | 腿2: EB2505
22:19:12.768 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257400 | 买单 | 价格: 80.0 | 数量: 4 | 腿1: EB2504 | 腿2: EB2505
22:19:12.768 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257401 | 买单 | 价格: 90.0 | 数量: 2 | 腿1: EB2504 | 腿2: EB2505
22:19:12.768 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257400 | 买单 | 价格: 85.0 | 数量: 2 | 腿1: EB2504 | 腿2: EB2505
22:19:39.923 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0
22:19:39.924 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (2/4)#0
22:19:39.923 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#0
22:19:39.923 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0
22:19:39.924 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (3/4)#0
22:19:39.924 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0
22:19:39.923 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (1/4)#0
22:19:39.924 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (3/4)#0
22:19:40.039 - SingleLegOrderDeserializer initialized
22:19:40.039 - ComboOrderDeserializer initialized
22:19:40.039 - ComboOrderDeserializer initialized
22:19:40.039 - SingleLegOrderDeserializer initialized
22:19:40.039 - ComboOrderDeserializer initialized
22:19:40.039 - SingleLegOrderDeserializer initialized
22:19:40.039 - SingleLegOrderDeserializer initialized
22:19:40.039 - ComboOrderDeserializer initialized
22:19:40.567 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:19:40.567 - ➕ [CS2505-C-2750] 新增/更新订单: 10000017 | 买单 | 价格: 13.0 | 数量: 1
22:19:40.567 - ➕ [JD2504-C-3200] 新增/更新订单: 10000000 | 买单 | 价格: 4.0 | 数量: 5
22:19:40.567 - ➕ [SH2505] 新增/更新订单: 10000021 | 买单 | 价格: 2690.0 | 数量: 1
22:19:40.567 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:19:40.567 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:19:40.567 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:19:40.568 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:19:40.568 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:19:40.568 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:19:40.568 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:19:40.568 - 🔧 [SH2505] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:19:40.568 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:19:40.568 - 🔧 [JD2504-C-3200] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:19:40.569 - 🔧 [CS2505-C-2750] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:19:40.569 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:19:40.569 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:19:40.569 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 14 | 腿1: EB2504 | 腿2: EB2505
22:19:40.570 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257400 | 买单 | 价格: 80.0 | 数量: 4 | 腿1: EB2504 | 腿2: EB2505
22:19:40.570 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257401 | 买单 | 价格: 90.0 | 数量: 2 | 腿1: EB2504 | 腿2: EB2505
22:19:40.570 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257400 | 买单 | 价格: 85.0 | 数量: 2 | 腿1: EB2504 | 腿2: EB2505
22:19:41.727 - SingleLegOrderDeserializer initialized
22:19:41.729 - SingleLegOrderDeserializer initialized
22:19:41.735 - SingleLegOrderDeserializer initialized
22:19:41.737 - SingleLegOrderDeserializer initialized
22:19:41.738 - ComboOrderDeserializer initialized
22:19:41.740 - ComboOrderDeserializer initialized
22:19:41.741 - ComboOrderDeserializer initialized
22:19:41.744 - ComboOrderDeserializer initialized
22:19:41.752 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#1
22:19:41.754 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#1
22:19:41.755 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#1
22:19:41.757 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (3/4)#1
22:19:41.759 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (1/4)#1
22:19:41.764 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (2/4)#1
22:19:41.765 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (3/4)#1
22:19:41.768 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#1
22:19:41.834 - ➕ [SH2505] 新增/更新订单: 10000021 | 买单 | 价格: 2690.0 | 数量: 1
22:19:41.834 - ➕ [JD2504-C-3200] 新增/更新订单: 10000000 | 买单 | 价格: 4.0 | 数量: 5
22:19:41.834 - ➕ [CS2505-C-2750] 新增/更新订单: 10000017 | 买单 | 价格: 13.0 | 数量: 1
22:19:41.834 - 🔧 [SH2505] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:19:41.834 - 🔧 [JD2504-C-3200] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:19:41.834 - 🔧 [CS2505-C-2750] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:19:43.254 - SingleLegOrderDeserializer initialized
22:19:43.255 - SingleLegOrderDeserializer initialized
22:19:43.256 - SingleLegOrderDeserializer initialized
22:19:43.257 - SingleLegOrderDeserializer initialized
22:19:43.260 - ComboOrderDeserializer initialized
22:19:43.260 - ComboOrderDeserializer initialized
22:19:43.260 - ComboOrderDeserializer initialized
22:19:43.262 - ComboOrderDeserializer initialized
22:19:43.273 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#2
22:19:43.275 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#2
22:19:43.275 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#2
22:19:43.278 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (3/4)#2
22:19:43.279 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (1/4)#2
22:19:43.280 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (2/4)#2
22:19:43.286 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (3/4)#2
22:19:43.288 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#2
22:19:43.355 - ➕ [JD2504-C-3200] 新增/更新订单: 10000000 | 买单 | 价格: 4.0 | 数量: 5
22:19:43.355 - ➕ [CS2505-C-2750] 新增/更新订单: 10000017 | 买单 | 价格: 13.0 | 数量: 1
22:19:43.355 - ➕ [SH2505] 新增/更新订单: 10000021 | 买单 | 价格: 2690.0 | 数量: 1
22:19:43.356 - 🔧 [JD2504-C-3200] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:19:43.356 - 🔧 [CS2505-C-2750] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:19:43.356 - 🔧 [SH2505] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:19:45.732 - SingleLegOrderDeserializer initialized
22:19:45.732 - SingleLegOrderDeserializer initialized
22:19:45.734 - SingleLegOrderDeserializer initialized
22:19:45.735 - SingleLegOrderDeserializer initialized
22:19:45.736 - ComboOrderDeserializer initialized
22:19:45.737 - ComboOrderDeserializer initialized
22:19:45.738 - ComboOrderDeserializer initialized
22:19:45.740 - ComboOrderDeserializer initialized
22:19:45.747 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#3
22:19:45.748 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#3
22:19:45.753 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#3
22:19:45.753 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (3/4)#3
22:19:45.755 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (1/4)#3
22:19:45.757 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (2/4)#3
22:19:45.758 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (3/4)#3
22:19:45.761 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#3
22:19:45.834 - ➕ [JD2504-C-3200] 新增/更新订单: 10000000 | 买单 | 价格: 4.0 | 数量: 5
22:19:45.834 - ➕ [SH2505] 新增/更新订单: 10000021 | 买单 | 价格: 2690.0 | 数量: 1
22:19:45.834 - ➕ [CS2505-C-2750] 新增/更新订单: 10000017 | 买单 | 价格: 13.0 | 数量: 1
22:19:45.835 - 🔧 [JD2504-C-3200] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:19:45.835 - 🔧 [SH2505] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:19:45.835 - 🔧 [CS2505-C-2750] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:19:49.090 - SingleLegOrderDeserializer initialized
22:19:49.091 - SingleLegOrderDeserializer initialized
22:19:49.093 - SingleLegOrderDeserializer initialized
22:19:49.093 - ComboOrderDeserializer initialized
22:19:49.092 - SingleLegOrderDeserializer initialized
22:19:49.095 - ComboOrderDeserializer initialized
22:19:49.096 - ComboOrderDeserializer initialized
22:19:49.097 - ComboOrderDeserializer initialized
22:19:49.102 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#4
22:19:49.104 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (3/4)#4
22:19:49.104 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#4
22:19:49.105 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#4
22:19:49.112 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (1/4)#4
22:19:49.114 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (3/4)#4
22:19:49.115 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (2/4)#4
22:19:49.118 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#4
22:19:49.193 - ➕ [JD2504-C-3200] 新增/更新订单: 10000000 | 买单 | 价格: 4.0 | 数量: 5
22:19:49.193 - ➕ [SH2505] 新增/更新订单: 10000021 | 买单 | 价格: 2690.0 | 数量: 1
22:19:49.193 - ➕ [CS2505-C-2750] 新增/更新订单: 10000017 | 买单 | 价格: 13.0 | 数量: 1
22:19:49.194 - 🔧 [JD2504-C-3200] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:19:49.194 - 🔧 [SH2505] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:19:49.194 - 🔧 [CS2505-C-2750] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:19:49.209 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:19:49.209 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:19:49.210 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:19:49.211 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:19:49.212 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:19:49.213 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:19:49.213 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:19:49.215 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:19:49.215 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:19:49.216 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:19:49.216 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:19:49.217 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 14 | 腿1: EB2504 | 腿2: EB2505
22:19:49.218 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257400 | 买单 | 价格: 80.0 | 数量: 4 | 腿1: EB2504 | 腿2: EB2505
22:19:49.219 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257401 | 买单 | 价格: 90.0 | 数量: 2 | 腿1: EB2504 | 腿2: EB2505
22:19:49.219 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257400 | 买单 | 价格: 85.0 | 数量: 2 | 腿1: EB2504 | 腿2: EB2505
22:19:54.522 - SingleLegOrderDeserializer initialized
22:19:54.523 - SingleLegOrderDeserializer initialized
22:19:54.524 - SingleLegOrderDeserializer initialized
22:19:54.525 - ComboOrderDeserializer initialized
22:19:54.526 - SingleLegOrderDeserializer initialized
22:19:54.527 - ComboOrderDeserializer initialized
22:19:54.527 - ComboOrderDeserializer initialized
22:19:54.528 - ComboOrderDeserializer initialized
22:19:54.536 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#5
22:19:54.537 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#5
22:19:54.540 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (3/4)#5
22:19:54.540 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#5
22:19:54.546 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (1/4)#5
22:19:54.548 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (2/4)#5
22:19:54.548 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#5
22:19:54.551 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (3/4)#5
22:19:54.623 - ➕ [CS2505-C-2750] 新增/更新订单: 10000017 | 买单 | 价格: 13.0 | 数量: 1
22:19:54.623 - ➕ [SH2505] 新增/更新订单: 10000021 | 买单 | 价格: 2690.0 | 数量: 1
22:19:54.623 - ➕ [JD2504-C-3200] 新增/更新订单: 10000000 | 买单 | 价格: 4.0 | 数量: 5
22:19:54.623 - 🔧 [CS2505-C-2750] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:19:54.623 - 🔧 [SH2505] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:19:54.623 - 🔧 [JD2504-C-3200] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:20:02.163 - SingleLegOrderDeserializer initialized
22:20:02.163 - SingleLegOrderDeserializer initialized
22:20:02.164 - SingleLegOrderDeserializer initialized
22:20:02.165 - SingleLegOrderDeserializer initialized
22:20:02.166 - ComboOrderDeserializer initialized
22:20:02.166 - ComboOrderDeserializer initialized
22:20:02.168 - ComboOrderDeserializer initialized
22:20:02.168 - ComboOrderDeserializer initialized
22:20:02.175 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#6
22:20:02.175 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#6
22:20:02.177 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#6
22:20:02.180 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (1/4)#6
22:20:02.180 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (3/4)#6
22:20:02.187 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (2/4)#6
22:20:02.187 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (3/4)#6
22:20:02.189 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#6
22:20:02.265 - ➕ [JD2504-C-3200] 新增/更新订单: 10000000 | 买单 | 价格: 4.0 | 数量: 5
22:20:02.265 - ➕ [CS2505-C-2750] 新增/更新订单: 10000017 | 买单 | 价格: 13.0 | 数量: 1
22:20:02.265 - ➕ [SH2505] 新增/更新订单: 10000021 | 买单 | 价格: 2690.0 | 数量: 1
22:20:02.266 - 🔧 [JD2504-C-3200] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:20:02.266 - 🔧 [CS2505-C-2750] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:20:02.266 - 🔧 [SH2505] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:20:13.724 - SingleLegOrderDeserializer initialized
22:20:13.724 - SingleLegOrderDeserializer initialized
22:20:13.725 - SingleLegOrderDeserializer initialized
22:20:13.726 - SingleLegOrderDeserializer initialized
22:20:13.728 - ComboOrderDeserializer initialized
22:20:13.729 - ComboOrderDeserializer initialized
22:20:13.730 - ComboOrderDeserializer initialized
22:20:13.730 - ComboOrderDeserializer initialized
22:20:13.738 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#7
22:20:13.738 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#7
22:20:13.740 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#7
22:20:13.740 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (3/4)#7
22:20:13.741 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (1/4)#7
22:20:13.750 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (2/4)#7
22:20:13.752 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#7
22:20:13.754 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (3/4)#7
22:20:13.826 - ➕ [JD2504-C-3200] 新增/更新订单: 10000000 | 买单 | 价格: 4.0 | 数量: 5
22:20:13.826 - ➕ [CS2505-C-2750] 新增/更新订单: 10000017 | 买单 | 价格: 13.0 | 数量: 1
22:20:13.826 - ➕ [SH2505] 新增/更新订单: 10000021 | 买单 | 价格: 2690.0 | 数量: 1
22:20:13.826 - 🔧 [JD2504-C-3200] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:20:13.826 - 🔧 [SH2505] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:20:13.826 - 🔧 [CS2505-C-2750] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:25:37.911 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (1/4)#0
22:25:37.911 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#0
22:25:37.911 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (3/4)#0
22:25:37.911 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (3/4)#0
22:25:37.911 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (2/4)#0
22:25:37.911 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0
22:25:37.911 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0
22:25:37.911 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0
22:25:37.999 - SingleLegOrderDeserializer initialized
22:25:37.999 - SingleLegOrderDeserializer initialized
22:25:37.999 - SingleLegOrderDeserializer initialized
22:25:37.999 - ComboOrderDeserializer initialized
22:25:37.999 - ComboOrderDeserializer initialized
22:25:37.999 - SingleLegOrderDeserializer initialized
22:25:37.999 - ComboOrderDeserializer initialized
22:25:37.999 - ComboOrderDeserializer initialized
22:25:38.556 - ➕ [CS2505-C-2750] 新增/更新订单: 10000017 | 买单 | 价格: 13.0 | 数量: 1
22:25:38.556 - ➕ [SH2505] 新增/更新订单: 10000021 | 买单 | 价格: 2690.0 | 数量: 1
22:25:38.556 - ➕ [JD2504-C-3200] 新增/更新订单: 10000000 | 买单 | 价格: 4.0 | 数量: 5
22:25:38.557 - 🔧 [SH2505] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:25:38.557 - 🔧 [CS2505-C-2750] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:25:38.557 - 🔧 [JD2504-C-3200] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:25:38.562 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:25:38.563 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:25:38.563 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:25:38.563 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:25:38.563 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:25:38.563 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:25:38.564 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:25:38.564 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:25:38.564 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:25:38.564 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:25:38.565 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:25:38.565 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 14 | 腿1: EB2504 | 腿2: EB2505
22:25:38.566 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257400 | 买单 | 价格: 80.0 | 数量: 4 | 腿1: EB2504 | 腿2: EB2505
22:25:38.566 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257401 | 买单 | 价格: 90.0 | 数量: 2 | 腿1: EB2504 | 腿2: EB2505
22:25:38.566 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257400 | 买单 | 价格: 85.0 | 数量: 2 | 腿1: EB2504 | 腿2: EB2505
22:25:39.727 - SingleLegOrderDeserializer initialized
22:25:39.728 - SingleLegOrderDeserializer initialized
22:25:39.729 - SingleLegOrderDeserializer initialized
22:25:39.730 - SingleLegOrderDeserializer initialized
22:25:39.732 - ComboOrderDeserializer initialized
22:25:39.733 - ComboOrderDeserializer initialized
22:25:39.739 - ComboOrderDeserializer initialized
22:25:39.744 - ComboOrderDeserializer initialized
22:25:39.755 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#1
22:25:39.757 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#1
22:25:39.758 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#1
22:25:39.760 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (3/4)#1
22:25:39.762 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (1/4)#1
22:25:39.765 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (2/4)#1
22:25:39.766 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (3/4)#1
22:25:39.771 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#1
22:25:39.836 - ➕ [SH2505] 新增/更新订单: 10000021 | 买单 | 价格: 2690.0 | 数量: 1
22:25:39.836 - ➕ [JD2504-C-3200] 新增/更新订单: 10000000 | 买单 | 价格: 4.0 | 数量: 5
22:25:39.836 - ➕ [CS2505-C-2750] 新增/更新订单: 10000017 | 买单 | 价格: 13.0 | 数量: 1
22:25:39.837 - 🔧 [JD2504-C-3200] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:25:39.837 - 🔧 [SH2505] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:25:39.837 - 🔧 [CS2505-C-2750] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:25:41.396 - SingleLegOrderDeserializer initialized
22:25:41.396 - SingleLegOrderDeserializer initialized
22:25:41.397 - SingleLegOrderDeserializer initialized
22:25:41.399 - ComboOrderDeserializer initialized
22:25:41.401 - SingleLegOrderDeserializer initialized
22:25:41.402 - ComboOrderDeserializer initialized
22:25:41.404 - ComboOrderDeserializer initialized
22:25:41.405 - ComboOrderDeserializer initialized
22:25:41.413 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#2
22:25:41.415 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (3/4)#2
22:25:41.417 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#2
22:25:41.418 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#2
22:25:41.426 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (2/4)#2
22:25:41.427 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (1/4)#2
22:25:41.431 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (3/4)#2
22:25:41.431 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#2
22:25:41.497 - ➕ [JD2504-C-3200] 新增/更新订单: 10000000 | 买单 | 价格: 4.0 | 数量: 5
22:25:41.497 - ➕ [SH2505] 新增/更新订单: 10000021 | 买单 | 价格: 2690.0 | 数量: 1
22:25:41.497 - ➕ [CS2505-C-2750] 新增/更新订单: 10000017 | 买单 | 价格: 13.0 | 数量: 1
22:25:41.498 - 🔧 [JD2504-C-3200] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:25:41.498 - 🔧 [SH2505] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:25:41.498 - 🔧 [CS2505-C-2750] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:25:43.657 - SingleLegOrderDeserializer initialized
22:25:43.657 - SingleLegOrderDeserializer initialized
22:25:43.658 - SingleLegOrderDeserializer initialized
22:25:43.659 - ComboOrderDeserializer initialized
22:25:43.660 - SingleLegOrderDeserializer initialized
22:25:43.662 - ComboOrderDeserializer initialized
22:25:43.664 - ComboOrderDeserializer initialized
22:25:43.667 - ComboOrderDeserializer initialized
22:25:43.673 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#3
22:25:43.674 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#3
22:25:43.676 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (3/4)#3
22:25:43.680 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#3
22:25:43.680 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (2/4)#3
22:25:43.684 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (1/4)#3
22:25:43.691 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#3
22:25:43.691 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (3/4)#3
22:25:43.759 - ➕ [CS2505-C-2750] 新增/更新订单: 10000017 | 买单 | 价格: 13.0 | 数量: 1
22:25:43.759 - ➕ [SH2505] 新增/更新订单: 10000021 | 买单 | 价格: 2690.0 | 数量: 1
22:25:43.759 - ➕ [JD2504-C-3200] 新增/更新订单: 10000000 | 买单 | 价格: 4.0 | 数量: 5
22:25:43.759 - 🔧 [SH2505] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:25:43.759 - 🔧 [CS2505-C-2750] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:25:43.759 - 🔧 [JD2504-C-3200] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:25:46.926 - SingleLegOrderDeserializer initialized
22:25:46.926 - SingleLegOrderDeserializer initialized
22:25:46.928 - SingleLegOrderDeserializer initialized
22:25:46.928 - SingleLegOrderDeserializer initialized
22:25:46.929 - ComboOrderDeserializer initialized
22:25:46.930 - ComboOrderDeserializer initialized
22:25:46.931 - ComboOrderDeserializer initialized
22:25:46.932 - ComboOrderDeserializer initialized
22:25:46.939 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#4
22:25:46.940 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#4
22:25:46.942 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (3/4)#4
22:25:46.943 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#4
22:25:46.945 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (1/4)#4
22:25:46.948 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (3/4)#4
22:25:46.950 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#4
22:25:46.950 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (2/4)#4
22:25:47.027 - ➕ [JD2504-C-3200] 新增/更新订单: 10000000 | 买单 | 价格: 4.0 | 数量: 5
22:25:47.027 - ➕ [CS2505-C-2750] 新增/更新订单: 10000017 | 买单 | 价格: 13.0 | 数量: 1
22:25:47.027 - ➕ [SH2505] 新增/更新订单: 10000021 | 买单 | 价格: 2690.0 | 数量: 1
22:25:47.028 - 🔧 [SH2505] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:25:47.028 - 🔧 [CS2505-C-2750] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:25:47.028 - 🔧 [JD2504-C-3200] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:25:52.175 - SingleLegOrderDeserializer initialized
22:25:52.176 - SingleLegOrderDeserializer initialized
22:25:52.177 - SingleLegOrderDeserializer initialized
22:25:52.177 - SingleLegOrderDeserializer initialized
22:25:52.179 - ComboOrderDeserializer initialized
22:25:52.180 - ComboOrderDeserializer initialized
22:25:52.180 - ComboOrderDeserializer initialized
22:25:52.181 - ComboOrderDeserializer initialized
22:25:52.189 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#5
22:25:52.190 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (3/4)#5
22:25:52.193 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#5
22:25:52.192 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#5
22:25:52.194 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (2/4)#5
22:25:52.197 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (1/4)#5
22:25:52.198 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#5
22:25:52.200 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (3/4)#5
22:25:52.278 - ➕ [CS2505-C-2750] 新增/更新订单: 10000017 | 买单 | 价格: 13.0 | 数量: 1
22:25:52.278 - ➕ [JD2504-C-3200] 新增/更新订单: 10000000 | 买单 | 价格: 4.0 | 数量: 5
22:25:52.278 - ➕ [SH2505] 新增/更新订单: 10000021 | 买单 | 价格: 2690.0 | 数量: 1
22:25:52.278 - 🔧 [JD2504-C-3200] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:25:52.278 - 🔧 [CS2505-C-2750] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:25:52.278 - 🔧 [SH2505] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:25:59.507 - SingleLegOrderDeserializer initialized
22:25:59.508 - SingleLegOrderDeserializer initialized
22:25:59.508 - SingleLegOrderDeserializer initialized
22:25:59.509 - SingleLegOrderDeserializer initialized
22:25:59.512 - ComboOrderDeserializer initialized
22:25:59.513 - ComboOrderDeserializer initialized
22:25:59.512 - ComboOrderDeserializer initialized
22:25:59.515 - ComboOrderDeserializer initialized
22:25:59.525 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#6
22:25:59.525 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#6
22:25:59.528 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (2/4)#6
22:25:59.528 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (1/4)#6
22:25:59.528 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (3/4)#6
22:25:59.537 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#6
22:25:59.538 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#6
22:25:59.546 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (3/4)#6
22:25:59.619 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:25:59.619 - ➕ [JD2504-C-3200] 新增/更新订单: 10000000 | 买单 | 价格: 4.0 | 数量: 5
22:25:59.619 - ➕ [SH2505] 新增/更新订单: 10000021 | 买单 | 价格: 2690.0 | 数量: 1
22:25:59.619 - ➕ [CS2505-C-2750] 新增/更新订单: 10000017 | 买单 | 价格: 13.0 | 数量: 1
22:25:59.620 - 🔧 [JD2504-C-3200] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:25:59.620 - 🔧 [SH2505] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:25:59.620 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:25:59.620 - 🔧 [CS2505-C-2750] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:25:59.620 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:25:59.621 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:25:59.621 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:25:59.621 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:25:59.621 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:25:59.621 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:25:59.622 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:25:59.622 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:25:59.623 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:25:59.623 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 14 | 腿1: EB2504 | 腿2: EB2505
22:25:59.624 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257400 | 买单 | 价格: 80.0 | 数量: 4 | 腿1: EB2504 | 腿2: EB2505
22:25:59.624 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257401 | 买单 | 价格: 90.0 | 数量: 2 | 腿1: EB2504 | 腿2: EB2505
22:25:59.625 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257400 | 买单 | 价格: 85.0 | 数量: 2 | 腿1: EB2504 | 腿2: EB2505
22:26:18.037 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/1)#0
22:26:18.037 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (1/1)#0
22:26:18.084 - SingleLegOrderDeserializer initialized
22:26:18.084 - ComboOrderDeserializer initialized
22:26:18.558 - ➕ [JD2504-C-3200] 新增/更新订单: 10000000 | 买单 | 价格: 4.0 | 数量: 5
22:26:18.558 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:18.559 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:18.559 - 🔧 [JD2504-C-3200] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:26:18.559 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:18.559 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:18.560 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:18.560 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:18.560 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:18.560 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:18.561 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:18.561 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:18.561 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:18.561 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 14 | 腿1: EB2504 | 腿2: EB2505
22:26:18.561 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257400 | 买单 | 价格: 80.0 | 数量: 4 | 腿1: EB2504 | 腿2: EB2505
22:26:18.562 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257401 | 买单 | 价格: 90.0 | 数量: 2 | 腿1: EB2504 | 腿2: EB2505
22:26:18.562 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257400 | 买单 | 价格: 85.0 | 数量: 2 | 腿1: EB2504 | 腿2: EB2505
22:26:19.665 - SingleLegOrderDeserializer initialized
22:26:19.665 - ComboOrderDeserializer initialized
22:26:19.675 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/1)#1
22:26:19.675 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (1/1)#1
22:26:19.772 - ➕ [JD2504-C-3200] 新增/更新订单: 10000000 | 买单 | 价格: 4.0 | 数量: 5
22:26:19.772 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:19.772 - 🔧 [JD2504-C-3200] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:26:19.772 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:19.772 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:19.772 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:19.772 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:19.773 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:19.773 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:19.773 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:19.773 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:19.774 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:19.774 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:19.774 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 14 | 腿1: EB2504 | 腿2: EB2505
22:26:19.774 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257400 | 买单 | 价格: 80.0 | 数量: 4 | 腿1: EB2504 | 腿2: EB2505
22:26:19.775 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257401 | 买单 | 价格: 90.0 | 数量: 2 | 腿1: EB2504 | 腿2: EB2505
22:26:19.775 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257400 | 买单 | 价格: 85.0 | 数量: 2 | 腿1: EB2504 | 腿2: EB2505
22:26:21.181 - SingleLegOrderDeserializer initialized
22:26:21.182 - ComboOrderDeserializer initialized
22:26:21.192 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/1)#2
22:26:21.194 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (1/1)#2
22:26:21.284 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:21.284 - ➕ [JD2504-C-3200] 新增/更新订单: 10000000 | 买单 | 价格: 4.0 | 数量: 5
22:26:21.285 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:21.285 - 🔧 [JD2504-C-3200] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:26:21.285 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:21.285 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:21.285 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:21.285 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:21.286 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:21.286 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:21.286 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:21.286 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:21.286 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:21.286 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 14 | 腿1: EB2504 | 腿2: EB2505
22:26:21.287 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257400 | 买单 | 价格: 80.0 | 数量: 4 | 腿1: EB2504 | 腿2: EB2505
22:26:21.287 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257401 | 买单 | 价格: 90.0 | 数量: 2 | 腿1: EB2504 | 腿2: EB2505
22:26:21.287 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257400 | 买单 | 价格: 85.0 | 数量: 2 | 腿1: EB2504 | 腿2: EB2505
22:26:23.521 - ComboOrderDeserializer initialized
22:26:23.522 - SingleLegOrderDeserializer initialized
22:26:23.538 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/1)#3
22:26:23.539 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (1/1)#3
22:26:23.624 - ➕ [JD2504-C-3200] 新增/更新订单: 10000000 | 买单 | 价格: 4.0 | 数量: 5
22:26:23.624 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:23.624 - 🔧 [JD2504-C-3200] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:26:23.624 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:23.624 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:23.625 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:23.625 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:23.625 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:23.625 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:23.626 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:23.626 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:23.626 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:23.626 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:23.627 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 14 | 腿1: EB2504 | 腿2: EB2505
22:26:23.627 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257400 | 买单 | 价格: 80.0 | 数量: 4 | 腿1: EB2504 | 腿2: EB2505
22:26:23.627 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257401 | 买单 | 价格: 90.0 | 数量: 2 | 腿1: EB2504 | 腿2: EB2505
22:26:23.627 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257400 | 买单 | 价格: 85.0 | 数量: 2 | 腿1: EB2504 | 腿2: EB2505
22:26:26.780 - SingleLegOrderDeserializer initialized
22:26:26.781 - ComboOrderDeserializer initialized
22:26:26.789 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/1)#4
22:26:26.790 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (1/1)#4
22:26:26.883 - ➕ [JD2504-C-3200] 新增/更新订单: 10000000 | 买单 | 价格: 4.0 | 数量: 5
22:26:26.883 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:26.883 - 🔧 [JD2504-C-3200] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:26:26.883 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:26.883 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:26.884 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:26.884 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:26.884 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:26.884 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:26.884 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:26.884 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:26.884 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:26.884 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:26.884 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 14 | 腿1: EB2504 | 腿2: EB2505
22:26:26.884 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257400 | 买单 | 价格: 80.0 | 数量: 4 | 腿1: EB2504 | 腿2: EB2505
22:26:26.884 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257401 | 买单 | 价格: 90.0 | 数量: 2 | 腿1: EB2504 | 腿2: EB2505
22:26:26.885 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257400 | 买单 | 价格: 85.0 | 数量: 2 | 腿1: EB2504 | 腿2: EB2505
22:26:32.226 - SingleLegOrderDeserializer initialized
22:26:32.226 - ComboOrderDeserializer initialized
22:26:32.237 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/1)#5
22:26:32.237 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (1/1)#5
22:26:32.329 - ➕ [JD2504-C-3200] 新增/更新订单: 10000000 | 买单 | 价格: 4.0 | 数量: 5
22:26:32.329 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:32.330 - 🔧 [JD2504-C-3200] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:26:32.330 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:32.330 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:32.331 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:32.331 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:32.331 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:32.332 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:32.332 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:32.332 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:32.332 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:32.332 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:32.332 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 14 | 腿1: EB2504 | 腿2: EB2505
22:26:32.333 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257400 | 买单 | 价格: 80.0 | 数量: 4 | 腿1: EB2504 | 腿2: EB2505
22:26:32.333 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257401 | 买单 | 价格: 90.0 | 数量: 2 | 腿1: EB2504 | 腿2: EB2505
22:26:32.333 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257400 | 买单 | 价格: 85.0 | 数量: 2 | 腿1: EB2504 | 腿2: EB2505
22:26:39.662 - SingleLegOrderDeserializer initialized
22:26:39.663 - ComboOrderDeserializer initialized
22:26:39.671 - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/1)#6
22:26:39.672 - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (1/1)#6
22:26:39.764 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:39.764 - ➕ [JD2504-C-3200] 新增/更新订单: 10000000 | 买单 | 价格: 4.0 | 数量: 5
22:26:39.765 - 🔧 [JD2504-C-3200] 订单簿构建 - 总订单: 1, 有效: 1, 买单: 1, 卖单: 0
22:26:39.765 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:39.765 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:39.765 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:39.766 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:39.766 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:39.766 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:39.766 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:39.767 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:39.767 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:39.767 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 13 | 腿1: EB2504 | 腿2: EB2505
22:26:39.767 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257414 | 买单 | 价格: 58.0 | 数量: 14 | 腿1: EB2504 | 腿2: EB2505
22:26:39.768 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257400 | 买单 | 价格: 80.0 | 数量: 4 | 腿1: EB2504 | 腿2: EB2505
22:26:39.768 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257401 | 买单 | 价格: 90.0 | 数量: 2 | 腿1: EB2504 | 腿2: EB2505
22:26:39.768 - 🔗 [EB2504-EB2505] 新增/更新组合订单: 100257400 | 买单 | 价格: 85.0 | 数量: 2 | 腿1: EB2504 | 腿2: EB2505
