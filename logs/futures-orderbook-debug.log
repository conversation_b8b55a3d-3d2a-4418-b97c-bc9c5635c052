2025-08-20 21:42:21.085 [Source: Combo Orders Stream (2/4)#0] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:21.085 [Source: Combo Orders Stream (4/4)#0] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:21.085 [Source: Combo Orders Stream (1/4)#0] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:21.085 [Source: SingleLeg Orders Stream (1/4)#0] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:21.085 [Source: Combo Orders Stream (3/4)#0] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:21.085 [Source: SingleLeg Orders Stream (3/4)#0] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:21.085 [Source: SingleLeg Orders Stream (4/4)#0] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:21.085 [Source: SingleLeg Orders Stream (2/4)#0] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:24.181 [Source: SingleLeg Orders Stream (1/4)#1] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:24.187 [Source: SingleLeg Orders Stream (3/4)#1] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:24.188 [Source: SingleLeg Orders Stream (2/4)#1] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:24.191 [Source: SingleLeg Orders Stream (4/4)#1] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:24.195 [Source: Combo Orders Stream (1/4)#1] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:24.197 [Source: Combo Orders Stream (3/4)#1] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:24.198 [Source: Combo Orders Stream (2/4)#1] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:24.203 [Source: Combo Orders Stream (4/4)#1] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:27.458 [Source: SingleLeg Orders Stream (1/4)#2] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:27.462 [Source: SingleLeg Orders Stream (2/4)#2] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:27.462 [Source: Combo Orders Stream (1/4)#2] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:27.464 [Source: SingleLeg Orders Stream (4/4)#2] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:27.464 [Source: SingleLeg Orders Stream (3/4)#2] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:27.464 [Source: Combo Orders Stream (2/4)#2] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:27.485 [Source: Combo Orders Stream (4/4)#2] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:27.500 [Source: Combo Orders Stream (3/4)#2] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:30.637 [Source: SingleLeg Orders Stream (2/4)#3] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:30.639 [Source: SingleLeg Orders Stream (1/4)#3] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:30.642 [Source: SingleLeg Orders Stream (3/4)#3] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:30.644 [Source: SingleLeg Orders Stream (4/4)#3] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:30.646 [Source: Combo Orders Stream (2/4)#3] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:30.646 [Source: Combo Orders Stream (1/4)#3] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:30.651 [Source: Combo Orders Stream (3/4)#3] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:30.652 [Source: Combo Orders Stream (4/4)#3] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:20.007 [Source: Combo Orders Stream (3/4)#0] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:20.007 [Source: SingleLeg Orders Stream (3/4)#0] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:20.007 [Source: Combo Orders Stream (1/4)#0] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:20.007 [Source: SingleLeg Orders Stream (4/4)#0] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:20.007 [Source: Combo Orders Stream (4/4)#0] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:20.007 [Source: SingleLeg Orders Stream (1/4)#0] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:20.007 [Source: SingleLeg Orders Stream (2/4)#0] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:20.007 [Source: Combo Orders Stream (2/4)#0] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:34.246 [Source: SingleLeg Orders Stream (1/4)#1] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:34.246 [Source: SingleLeg Orders Stream (2/4)#1] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:34.251 [Source: SingleLeg Orders Stream (3/4)#1] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:34.255 [Source: SingleLeg Orders Stream (4/4)#1] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:34.256 [Source: Combo Orders Stream (1/4)#1] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:34.260 [Source: Combo Orders Stream (2/4)#1] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:34.262 [Source: Combo Orders Stream (3/4)#1] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:34.282 [Source: Combo Orders Stream (4/4)#1] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:34.288 [Source: SingleLeg Orders Stream (2/4)#1] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:34.288 [Source: Combo Orders Stream (4/4)#1] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:57.317 [Source: SingleLeg Orders Stream (3/4)#2] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:57.317 [Source: SingleLeg Orders Stream (2/4)#2] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:57.318 [Source: SingleLeg Orders Stream (1/4)#2] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:57.324 [Source: SingleLeg Orders Stream (4/4)#2] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:57.325 [Source: Combo Orders Stream (1/4)#2] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:57.334 [Source: Combo Orders Stream (3/4)#2] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:57.334 [Source: Combo Orders Stream (2/4)#2] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:57.344 [Source: Combo Orders Stream (4/4)#2] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:57.349 [Source: SingleLeg Orders Stream (2/4)#2] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:57.349 [Source: Combo Orders Stream (4/4)#2] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:13.383 [Source: SingleLeg Orders Stream (1/4)#3] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:13.385 [Source: SingleLeg Orders Stream (3/4)#3] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:13.385 [Source: SingleLeg Orders Stream (2/4)#3] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:13.385 [Source: SingleLeg Orders Stream (4/4)#3] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:13.389 [Source: Combo Orders Stream (1/4)#3] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:13.391 [Source: Combo Orders Stream (2/4)#3] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:13.394 [Source: Combo Orders Stream (4/4)#3] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:13.394 [Source: Combo Orders Stream (3/4)#3] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:13.404 [Source: SingleLeg Orders Stream (2/4)#3] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:13.405 [Source: Combo Orders Stream (4/4)#3] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:30.159 [Source: SingleLeg Orders Stream (2/4)#4] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:30.159 [Source: SingleLeg Orders Stream (1/4)#4] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:30.160 [Source: SingleLeg Orders Stream (3/4)#4] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:30.163 [Source: SingleLeg Orders Stream (4/4)#4] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:30.165 [Source: Combo Orders Stream (1/4)#4] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:30.166 [Source: Combo Orders Stream (2/4)#4] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:30.168 [Source: Combo Orders Stream (3/4)#4] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:30.170 [Source: Combo Orders Stream (4/4)#4] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:30.180 [Source: SingleLeg Orders Stream (2/4)#4] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:30.180 [Source: Combo Orders Stream (4/4)#4] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:44.732 [Source: SingleLeg Orders Stream (2/4)#5] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:44.732 [Source: SingleLeg Orders Stream (3/4)#5] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:44.733 [Source: SingleLeg Orders Stream (1/4)#5] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:44.735 [Source: SingleLeg Orders Stream (4/4)#5] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:44.737 [Source: Combo Orders Stream (1/4)#5] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:44.739 [Source: Combo Orders Stream (2/4)#5] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:44.741 [Source: Combo Orders Stream (3/4)#5] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:44.741 [Source: Combo Orders Stream (4/4)#5] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:44.748 [Source: SingleLeg Orders Stream (2/4)#5] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:44.748 [Source: Combo Orders Stream (4/4)#5] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:57.694 [Source: SingleLeg Orders Stream (1/4)#6] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:57.695 [Source: SingleLeg Orders Stream (3/4)#6] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:57.695 [Source: SingleLeg Orders Stream (2/4)#6] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:57.696 [Source: SingleLeg Orders Stream (4/4)#6] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:57.699 [Source: Combo Orders Stream (1/4)#6] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:57.701 [Source: Combo Orders Stream (2/4)#6] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:57.703 [Source: Combo Orders Stream (4/4)#6] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:57.704 [Source: Combo Orders Stream (3/4)#6] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:57.714 [Source: SingleLeg Orders Stream (2/4)#6] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:57.714 [Source: Combo Orders Stream (4/4)#6] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 22:00:10.571 [Source: SingleLeg Orders Stream (1/4)#7] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 22:00:10.571 [Source: SingleLeg Orders Stream (2/4)#7] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 22:00:10.572 [Source: SingleLeg Orders Stream (3/4)#7] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 22:00:10.573 [Source: SingleLeg Orders Stream (4/4)#7] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 22:00:10.578 [Source: Combo Orders Stream (3/4)#7] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 22:00:10.579 [Source: Combo Orders Stream (2/4)#7] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 22:00:10.579 [Source: Combo Orders Stream (1/4)#7] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 22:00:10.580 [Source: Combo Orders Stream (4/4)#7] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 22:00:10.595 [Source: SingleLeg Orders Stream (2/4)#7] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 22:00:10.596 [Source: Combo Orders Stream (4/4)#7] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 22:01:54.728 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#0] INFO  com.futures.function.BaseOrderBookBuilder - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#0
2025-08-20 22:01:54.728 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (3/4)#0] INFO  com.futures.function.BaseOrderBookBuilder - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (3/4)#0
2025-08-20 22:01:54.728 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] INFO  com.futures.function.VirtualOrderBookBuilder - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0
2025-08-20 22:01:54.728 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] INFO  com.futures.function.BaseOrderBookBuilder - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0
2025-08-20 22:01:54.728 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (3/4)#0] INFO  com.futures.function.VirtualOrderBookBuilder - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (3/4)#0
2025-08-20 22:01:54.728 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (2/4)#0] INFO  com.futures.function.VirtualOrderBookBuilder - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (2/4)#0
2025-08-20 22:01:54.728 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] INFO  com.futures.function.BaseOrderBookBuilder - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0
2025-08-20 22:01:54.728 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (1/4)#0] INFO  com.futures.function.VirtualOrderBookBuilder - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (1/4)#0
2025-08-20 22:01:54.898 [Source: SingleLeg Orders Stream (1/4)#0] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 22:01:54.898 [Source: Combo Orders Stream (2/4)#0] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 22:01:54.898 [Source: SingleLeg Orders Stream (3/4)#0] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 22:01:54.898 [Source: SingleLeg Orders Stream (4/4)#0] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 22:01:54.898 [Source: Combo Orders Stream (4/4)#0] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 22:01:54.898 [Source: SingleLeg Orders Stream (2/4)#0] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 22:01:54.898 [Source: Combo Orders Stream (3/4)#0] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 22:01:54.898 [Source: Combo Orders Stream (1/4)#0] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 22:01:55.040 [Source: Combo Orders Stream (4/4)#0] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 22:01:55.040 [Source: SingleLeg Orders Stream (2/4)#0] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 22:01:55.374 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] DEBUG com.futures.function.VirtualOrderBookBuilder - Processing combo order for EB2504-EB2505: 100257414
2025-08-20 22:01:55.374 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract CS2505-C-2750: 10000017
2025-08-20 22:01:55.374 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract JD2504-C-3200: 10000000
2025-08-20 22:01:55.374 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract SH2505: 10000021
2025-08-20 22:01:55.388 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='SH2505', ordNbr='10000021', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698276773, expiryTimestamp=0}
2025-08-20 22:01:55.388 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='CS2505-C-2750', ordNbr='10000017', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698275405, expiryTimestamp=0}
2025-08-20 22:01:55.388 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='CS2505-C-2750', ordNbr='10000017', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698275405, expiryTimestamp=0}
2025-08-20 22:01:55.388 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='SH2505', ordNbr='10000021', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698276773, expiryTimestamp=0}
2025-08-20 22:01:55.388 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000000', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698273857, expiryTimestamp=0}
2025-08-20 22:01:55.388 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract CS2505-C-2750: 10000017
2025-08-20 22:01:55.388 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000000', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698273857, expiryTimestamp=0}
2025-08-20 22:01:55.388 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='CS2505-C-2750', ordNbr='10000017', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698275529, expiryTimestamp=0}
2025-08-20 22:01:55.388 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract SH2505: 10000021
2025-08-20 22:01:55.388 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='CS2505-C-2750', ordNbr='10000017', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698275529, expiryTimestamp=0}
2025-08-20 22:01:55.388 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='SH2505', ordNbr='10000021', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698276895, expiryTimestamp=0}
2025-08-20 22:01:55.388 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Combo order has invalid BS tag: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=13, timestamp=1755698284349}
2025-08-20 22:01:55.388 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='SH2505', ordNbr='10000021', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698276895, expiryTimestamp=0}
2025-08-20 22:01:55.388 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract CS2505-C-2750: 10000019
2025-08-20 22:01:55.388 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract JD2504-C-3200: 10000001
2025-08-20 22:01:55.389 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='CS2505-C-2750', ordNbr='10000019', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698275652, expiryTimestamp=0}
2025-08-20 22:01:55.388 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Invalid combo order received: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=13, timestamp=1755698284349}
2025-08-20 22:01:55.389 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='CS2505-C-2750', ordNbr='10000019', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698275652, expiryTimestamp=0}
2025-08-20 22:01:55.389 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000001', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698274413, expiryTimestamp=0}
2025-08-20 22:01:55.389 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract SH2505: 10000023
2025-08-20 22:01:55.389 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000001', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698274413, expiryTimestamp=0}
2025-08-20 22:01:55.389 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='SH2505', ordNbr='10000023', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698277018, expiryTimestamp=0}
2025-08-20 22:01:55.389 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] DEBUG com.futures.function.VirtualOrderBookBuilder - Processing combo order for EB2504-EB2505: 100257414
2025-08-20 22:01:55.389 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract CS2505-C-2750: 10000019
2025-08-20 22:01:55.389 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='SH2505', ordNbr='10000023', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698277018, expiryTimestamp=0}
2025-08-20 22:01:55.389 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Combo order has invalid BS tag: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=12, timestamp=1755698284591}
2025-08-20 22:01:55.389 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='CS2505-C-2750', ordNbr='10000019', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698275776, expiryTimestamp=0}
2025-08-20 22:01:55.389 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract JD2504-C-3200: 10000001
2025-08-20 22:01:55.389 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Invalid combo order received: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=12, timestamp=1755698284591}
2025-08-20 22:01:55.389 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='CS2505-C-2750', ordNbr='10000019', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698275776, expiryTimestamp=0}
2025-08-20 22:01:55.389 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] DEBUG com.futures.function.VirtualOrderBookBuilder - Processing combo order for EB2504-EB2505: 100257414
2025-08-20 22:01:55.389 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Combo order has invalid BS tag: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=11, timestamp=1755698284713}
2025-08-20 22:01:55.389 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100257097
2025-08-20 22:01:55.389 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Invalid combo order received: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=11, timestamp=1755698284713}
2025-08-20 22:01:55.389 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000001', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698274535, expiryTimestamp=0}
2025-08-20 22:01:55.389 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract SH2505: 10000023
2025-08-20 22:01:55.389 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257097', bSTag='null', trdPrc=0.0, rmnVol=10, timestamp=1755698276027, expiryTimestamp=0}
2025-08-20 22:01:55.389 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000001', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698274535, expiryTimestamp=0}
2025-08-20 22:01:55.389 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='SH2505', ordNbr='10000023', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698277144, expiryTimestamp=0}
2025-08-20 22:01:55.389 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257097', bSTag='null', trdPrc=0.0, rmnVol=10, timestamp=1755698276027, expiryTimestamp=0}
2025-08-20 22:01:55.389 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='SH2505', ordNbr='10000023', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698277144, expiryTimestamp=0}
2025-08-20 22:01:55.389 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract JD2504-C-3200: 10000005
2025-08-20 22:01:55.389 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] DEBUG com.futures.function.VirtualOrderBookBuilder - Processing combo order for EB2504-EB2505: 100257414
2025-08-20 22:01:55.389 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000005', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698274659, expiryTimestamp=0}
2025-08-20 22:01:55.389 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100257007
2025-08-20 22:01:55.389 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Combo order has invalid BS tag: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=10, timestamp=1755698284839}
2025-08-20 22:01:55.390 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000005', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698274659, expiryTimestamp=0}
2025-08-20 22:01:55.390 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257007', bSTag='null', trdPrc=0.0, rmnVol=10, timestamp=1755698276148, expiryTimestamp=0}
2025-08-20 22:01:55.390 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257007', bSTag='null', trdPrc=0.0, rmnVol=10, timestamp=1755698276148, expiryTimestamp=0}
2025-08-20 22:01:55.390 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Invalid combo order received: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=10, timestamp=1755698284839}
2025-08-20 22:01:55.390 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] DEBUG com.futures.function.VirtualOrderBookBuilder - Processing combo order for EB2504-EB2505: 100257414
2025-08-20 22:01:55.390 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100257007
2025-08-20 22:01:55.390 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Combo order has invalid BS tag: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=9, timestamp=1755698284962}
2025-08-20 22:01:55.390 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract JD2504-C-3200: 10000005
2025-08-20 22:01:55.390 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257007', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698276273, expiryTimestamp=0}
2025-08-20 22:01:55.390 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Invalid combo order received: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=9, timestamp=1755698284962}
2025-08-20 22:01:55.390 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000005', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698274783, expiryTimestamp=0}
2025-08-20 22:01:55.390 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257007', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698276273, expiryTimestamp=0}
2025-08-20 22:01:55.390 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000005', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698274783, expiryTimestamp=0}
2025-08-20 22:01:55.390 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] DEBUG com.futures.function.VirtualOrderBookBuilder - Processing combo order for EB2504-EB2505: 100257414
2025-08-20 22:01:55.390 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100257004
2025-08-20 22:01:55.390 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Combo order has invalid BS tag: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=8, timestamp=1755698285086}
2025-08-20 22:01:55.390 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Invalid combo order received: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=8, timestamp=1755698285086}
2025-08-20 22:01:55.390 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257004', bSTag='null', trdPrc=0.0, rmnVol=10, timestamp=1755698276399, expiryTimestamp=0}
2025-08-20 22:01:55.390 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract JD2504-C-3200: 10000009
2025-08-20 22:01:55.390 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257004', bSTag='null', trdPrc=0.0, rmnVol=10, timestamp=1755698276399, expiryTimestamp=0}
2025-08-20 22:01:55.390 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] DEBUG com.futures.function.VirtualOrderBookBuilder - Processing combo order for EB2504-EB2505: 100257414
2025-08-20 22:01:55.390 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000009', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698274910, expiryTimestamp=0}
2025-08-20 22:01:55.390 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100257004
2025-08-20 22:01:55.390 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Combo order has invalid BS tag: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=7, timestamp=1755698285210}
2025-08-20 22:01:55.390 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257004', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698276524, expiryTimestamp=0}
2025-08-20 22:01:55.390 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000009', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698274910, expiryTimestamp=0}
2025-08-20 22:01:55.390 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Invalid combo order received: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=7, timestamp=1755698285210}
2025-08-20 22:01:55.391 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract JD2504-C-3200: 10000009
2025-08-20 22:01:55.390 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257004', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698276524, expiryTimestamp=0}
2025-08-20 22:01:55.391 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000009', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698275032, expiryTimestamp=0}
2025-08-20 22:01:55.391 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] DEBUG com.futures.function.VirtualOrderBookBuilder - Processing combo order for EB2504-EB2505: 100257414
2025-08-20 22:01:55.391 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100257098
2025-08-20 22:01:55.391 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Combo order has invalid BS tag: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=6, timestamp=1755698285335}
2025-08-20 22:01:55.391 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000009', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698275032, expiryTimestamp=0}
2025-08-20 22:01:55.391 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257098', bSTag='null', trdPrc=0.0, rmnVol=3, timestamp=1755698276648, expiryTimestamp=0}
2025-08-20 22:01:55.391 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257098', bSTag='null', trdPrc=0.0, rmnVol=3, timestamp=1755698276648, expiryTimestamp=0}
2025-08-20 22:01:55.391 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract JD2504-C-3200: 10000011
2025-08-20 22:01:55.391 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Invalid combo order received: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=6, timestamp=1755698285335}
2025-08-20 22:01:55.391 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000011', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698275155, expiryTimestamp=0}
2025-08-20 22:01:55.391 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2504: 100257000
2025-08-20 22:01:55.391 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000011', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698275155, expiryTimestamp=0}
2025-08-20 22:01:55.391 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257000', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698277268, expiryTimestamp=0}
2025-08-20 22:01:55.391 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] DEBUG com.futures.function.VirtualOrderBookBuilder - Processing combo order for EB2504-EB2505: 100257414
2025-08-20 22:01:55.391 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257000', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698277268, expiryTimestamp=0}
2025-08-20 22:01:55.391 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract JD2504-C-3200: 10000011
2025-08-20 22:01:55.391 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Combo order has invalid BS tag: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=4, timestamp=1755698285459}
2025-08-20 22:01:55.391 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000011', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698275280, expiryTimestamp=0}
2025-08-20 22:01:55.391 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Invalid combo order received: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=4, timestamp=1755698285459}
2025-08-20 22:01:55.391 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2504: 100257001
2025-08-20 22:01:55.391 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000011', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698275280, expiryTimestamp=0}
2025-08-20 22:01:55.391 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257001', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698277392, expiryTimestamp=0}
2025-08-20 22:01:55.391 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] DEBUG com.futures.function.VirtualOrderBookBuilder - Processing combo order for EB2504-EB2505: 100257414
2025-08-20 22:01:55.391 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257001', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698277392, expiryTimestamp=0}
2025-08-20 22:01:55.391 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Combo order has invalid BS tag: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698285583}
2025-08-20 22:01:55.391 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract JD2504-C-3200: 10000000
2025-08-20 22:01:55.392 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Invalid combo order received: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698285583}
2025-08-20 22:01:55.392 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000000', bSTag='null', trdPrc=0.0, rmnVol=3, timestamp=1755698275901, expiryTimestamp=0}
2025-08-20 22:01:55.392 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2504: 100257002
2025-08-20 22:01:55.392 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000000', bSTag='null', trdPrc=0.0, rmnVol=3, timestamp=1755698275901, expiryTimestamp=0}
2025-08-20 22:01:55.392 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] DEBUG com.futures.function.VirtualOrderBookBuilder - Processing combo order for EB2504-EB2505: 100257414
2025-08-20 22:01:55.392 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257002', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698277516, expiryTimestamp=0}
2025-08-20 22:01:55.392 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Combo order has invalid BS tag: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698285707}
2025-08-20 22:01:55.392 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257002', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698277516, expiryTimestamp=0}
2025-08-20 22:01:55.392 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Invalid combo order received: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698285707}
2025-08-20 22:01:55.392 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2504: 100257003
2025-08-20 22:01:55.392 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] DEBUG com.futures.function.VirtualOrderBookBuilder - Processing combo order for EB2504-EB2505: 100257414
2025-08-20 22:01:55.392 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257003', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698277644, expiryTimestamp=0}
2025-08-20 22:01:55.392 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Combo order has invalid BS tag: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698285831}
2025-08-20 22:01:55.392 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257003', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698277644, expiryTimestamp=0}
2025-08-20 22:01:55.392 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Invalid combo order received: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698285831}
2025-08-20 22:01:55.392 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100257009
2025-08-20 22:01:55.392 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] DEBUG com.futures.function.VirtualOrderBookBuilder - Processing combo order for EB2504-EB2505: 100257400
2025-08-20 22:01:55.392 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257009', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698277766, expiryTimestamp=0}
2025-08-20 22:01:55.392 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Combo order has invalid BS tag: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257400', bSTag='null', trdPrc=0.0, rmnVol=4, timestamp=1755698285954}
2025-08-20 22:01:55.392 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257009', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698277766, expiryTimestamp=0}
2025-08-20 22:01:55.392 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Invalid combo order received: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257400', bSTag='null', trdPrc=0.0, rmnVol=4, timestamp=1755698285954}
2025-08-20 22:01:55.392 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100257005
2025-08-20 22:01:55.392 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] DEBUG com.futures.function.VirtualOrderBookBuilder - Processing combo order for EB2504-EB2505: 100257401
2025-08-20 22:01:55.392 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257005', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698277891, expiryTimestamp=0}
2025-08-20 22:01:55.392 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Combo order has invalid BS tag: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257401', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698286078}
2025-08-20 22:01:55.392 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257005', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698277891, expiryTimestamp=0}
2025-08-20 22:01:55.392 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Invalid combo order received: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257401', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698286078}
2025-08-20 22:01:55.392 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100257006
2025-08-20 22:01:55.393 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] DEBUG com.futures.function.VirtualOrderBookBuilder - Processing combo order for EB2504-EB2505: 100257400
2025-08-20 22:01:55.393 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257006', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698278013, expiryTimestamp=0}
2025-08-20 22:01:55.393 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Combo order has invalid BS tag: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257400', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698286202}
2025-08-20 22:01:55.393 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Invalid combo order received: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257400', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698286202}
2025-08-20 22:01:55.393 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257006', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698278013, expiryTimestamp=0}
2025-08-20 22:01:55.393 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100257008
2025-08-20 22:01:55.393 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257008', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698278136, expiryTimestamp=0}
2025-08-20 22:01:55.393 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257008', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698278136, expiryTimestamp=0}
2025-08-20 22:01:55.393 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100256966
2025-08-20 22:01:55.393 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100256966', bSTag='null', trdPrc=0.0, rmnVol=9, timestamp=1755698278259, expiryTimestamp=0}
2025-08-20 22:01:55.393 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100256966', bSTag='null', trdPrc=0.0, rmnVol=9, timestamp=1755698278259, expiryTimestamp=0}
2025-08-20 22:01:55.393 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100257085
2025-08-20 22:01:55.393 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257085', bSTag='null', trdPrc=0.0, rmnVol=8, timestamp=1755698278383, expiryTimestamp=0}
2025-08-20 22:01:55.393 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257085', bSTag='null', trdPrc=0.0, rmnVol=8, timestamp=1755698278383, expiryTimestamp=0}
2025-08-20 22:01:55.393 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100257085
2025-08-20 22:01:55.394 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257085', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698278507, expiryTimestamp=0}
2025-08-20 22:01:55.394 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257085', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698278507, expiryTimestamp=0}
2025-08-20 22:01:55.394 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100256966
2025-08-20 22:01:55.394 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100256966', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698278630, expiryTimestamp=0}
2025-08-20 22:01:55.394 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100256966', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698278630, expiryTimestamp=0}
2025-08-20 22:01:55.394 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100256966
2025-08-20 22:01:55.394 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100256966', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698278754, expiryTimestamp=0}
2025-08-20 22:01:55.394 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100256966', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698278754, expiryTimestamp=0}
2025-08-20 22:01:55.394 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100257259
2025-08-20 22:01:55.394 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257259', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698278878, expiryTimestamp=0}
2025-08-20 22:01:55.394 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257259', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698278878, expiryTimestamp=0}
2025-08-20 22:01:55.394 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100257259
2025-08-20 22:01:55.394 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257259', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698279003, expiryTimestamp=0}
2025-08-20 22:01:55.394 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257259', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698279003, expiryTimestamp=0}
2025-08-20 22:01:55.395 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100256966
2025-08-20 22:01:55.395 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100256966', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698279127, expiryTimestamp=0}
2025-08-20 22:01:55.395 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100256966', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698279127, expiryTimestamp=0}
2025-08-20 22:01:55.395 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100254367
2025-08-20 22:01:55.395 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100254367', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698279251, expiryTimestamp=0}
2025-08-20 22:01:55.395 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100254367', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698279251, expiryTimestamp=0}
2025-08-20 22:01:55.395 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100254367
2025-08-20 22:01:55.395 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100254367', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698279375, expiryTimestamp=0}
2025-08-20 22:01:55.395 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100254367', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698279375, expiryTimestamp=0}
2025-08-20 22:01:55.395 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2504: 100257479
2025-08-20 22:01:55.395 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257479', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698279498, expiryTimestamp=0}
2025-08-20 22:01:55.395 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257479', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698279498, expiryTimestamp=0}
2025-08-20 22:01:55.395 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2504: 100257479
2025-08-20 22:01:55.395 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257479', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698279623, expiryTimestamp=0}
2025-08-20 22:01:55.395 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257479', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698279623, expiryTimestamp=0}
2025-08-20 22:01:55.395 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2504: 100256192
2025-08-20 22:01:55.395 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100256192', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698279747, expiryTimestamp=0}
2025-08-20 22:01:55.395 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100256192', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698279747, expiryTimestamp=0}
2025-08-20 22:01:55.395 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2504: 100256192
2025-08-20 22:01:55.395 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100256192', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698279871, expiryTimestamp=0}
2025-08-20 22:01:55.395 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100256192', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698279871, expiryTimestamp=0}
2025-08-20 22:01:55.395 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100257507
2025-08-20 22:01:55.395 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257507', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698279995, expiryTimestamp=0}
2025-08-20 22:01:55.395 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257507', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698279995, expiryTimestamp=0}
2025-08-20 22:01:55.395 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100257507
2025-08-20 22:01:55.395 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257507', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698280120, expiryTimestamp=0}
2025-08-20 22:01:55.395 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257507', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698280120, expiryTimestamp=0}
2025-08-20 22:01:55.396 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100257466
2025-08-20 22:01:55.396 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257466', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698280243, expiryTimestamp=0}
2025-08-20 22:01:55.396 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257466', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698280243, expiryTimestamp=0}
2025-08-20 22:01:55.396 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100257466
2025-08-20 22:01:55.396 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257466', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698280367, expiryTimestamp=0}
2025-08-20 22:01:55.396 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257466', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698280367, expiryTimestamp=0}
2025-08-20 22:01:55.396 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100257500
2025-08-20 22:01:55.396 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257500', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698280490, expiryTimestamp=0}
2025-08-20 22:01:55.396 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257500', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698280490, expiryTimestamp=0}
2025-08-20 22:01:55.396 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100257500
2025-08-20 22:01:55.396 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257500', bSTag='null', trdPrc=0.0, rmnVol=3, timestamp=1755698280614, expiryTimestamp=0}
2025-08-20 22:01:55.396 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257500', bSTag='null', trdPrc=0.0, rmnVol=3, timestamp=1755698280614, expiryTimestamp=0}
2025-08-20 22:01:55.396 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2504: 100257560
2025-08-20 22:01:55.396 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257560', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698280737, expiryTimestamp=0}
2025-08-20 22:01:55.396 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257560', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698280737, expiryTimestamp=0}
2025-08-20 22:01:55.397 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2504: 100257560
2025-08-20 22:01:55.397 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257560', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698280860, expiryTimestamp=0}
2025-08-20 22:01:55.397 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257560', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698280860, expiryTimestamp=0}
2025-08-20 22:01:55.397 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2504: 100257560
2025-08-20 22:01:55.397 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257560', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698280985, expiryTimestamp=0}
2025-08-20 22:01:55.397 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257560', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698280985, expiryTimestamp=0}
2025-08-20 22:01:55.397 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100257500
2025-08-20 22:01:55.397 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257500', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698281110, expiryTimestamp=0}
2025-08-20 22:01:55.397 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257500', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698281110, expiryTimestamp=0}
2025-08-20 22:01:55.397 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100257500
2025-08-20 22:01:55.397 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257500', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698281235, expiryTimestamp=0}
2025-08-20 22:01:55.397 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257500', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698281235, expiryTimestamp=0}
2025-08-20 22:01:55.397 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2504: 100257608
2025-08-20 22:01:55.397 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257608', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698281359, expiryTimestamp=0}
2025-08-20 22:01:55.397 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257608', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698281359, expiryTimestamp=0}
2025-08-20 22:01:55.397 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2504: 100257608
2025-08-20 22:01:55.397 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257608', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698281482, expiryTimestamp=0}
2025-08-20 22:01:55.398 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257608', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698281482, expiryTimestamp=0}
2025-08-20 22:01:55.398 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2504: 100257648
2025-08-20 22:01:55.398 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257648', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698281607, expiryTimestamp=0}
2025-08-20 22:01:55.398 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257648', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698281607, expiryTimestamp=0}
2025-08-20 22:01:55.398 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2504: 100257648
2025-08-20 22:01:55.398 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257648', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698281732, expiryTimestamp=0}
2025-08-20 22:01:55.398 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257648', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698281732, expiryTimestamp=0}
2025-08-20 22:01:55.398 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2504: 100257648
2025-08-20 22:01:55.398 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257648', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698281857, expiryTimestamp=0}
2025-08-20 22:01:55.398 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257648', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698281857, expiryTimestamp=0}
2025-08-20 22:01:55.398 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100257736
2025-08-20 22:01:55.398 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257736', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698281981, expiryTimestamp=0}
2025-08-20 22:01:55.398 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257736', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698281981, expiryTimestamp=0}
2025-08-20 22:01:55.398 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100257736
2025-08-20 22:01:55.398 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257736', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698282105, expiryTimestamp=0}
2025-08-20 22:01:55.398 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257736', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698282105, expiryTimestamp=0}
2025-08-20 22:01:55.399 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2504: 100257414
2025-08-20 22:01:55.399 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=10, timestamp=1755698282229, expiryTimestamp=0}
2025-08-20 22:01:55.399 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=10, timestamp=1755698282229, expiryTimestamp=0}
2025-08-20 22:01:55.399 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2504: 100257414
2025-08-20 22:01:55.399 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698282353, expiryTimestamp=0}
2025-08-20 22:01:55.399 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698282353, expiryTimestamp=0}
2025-08-20 22:01:55.399 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2504: 100257342
2025-08-20 22:01:55.399 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257342', bSTag='null', trdPrc=0.0, rmnVol=10, timestamp=1755698282477, expiryTimestamp=0}
2025-08-20 22:01:55.399 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257342', bSTag='null', trdPrc=0.0, rmnVol=10, timestamp=1755698282477, expiryTimestamp=0}
2025-08-20 22:01:55.399 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2504: 100257342
2025-08-20 22:01:55.399 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257342', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698282602, expiryTimestamp=0}
2025-08-20 22:01:55.399 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257342', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698282602, expiryTimestamp=0}
2025-08-20 22:01:55.399 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100258082
2025-08-20 22:01:55.399 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100258082', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698282727, expiryTimestamp=0}
2025-08-20 22:01:55.399 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100258082', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698282727, expiryTimestamp=0}
2025-08-20 22:01:55.399 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100258082
2025-08-20 22:01:55.399 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100258082', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698282851, expiryTimestamp=0}
2025-08-20 22:01:55.399 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100258082', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698282851, expiryTimestamp=0}
2025-08-20 22:01:55.399 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100258081
2025-08-20 22:01:55.399 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100258081', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698282976, expiryTimestamp=0}
2025-08-20 22:01:55.399 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100258081', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698282976, expiryTimestamp=0}
2025-08-20 22:01:55.400 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100258083
2025-08-20 22:01:55.400 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100258083', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698283100, expiryTimestamp=0}
2025-08-20 22:01:55.400 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100258083', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698283100, expiryTimestamp=0}
2025-08-20 22:01:55.400 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2504: 100257349
2025-08-20 22:01:55.400 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257349', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698283224, expiryTimestamp=0}
2025-08-20 22:01:55.400 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257349', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698283224, expiryTimestamp=0}
2025-08-20 22:02:05.019 [Source: SingleLeg Orders Stream (4/4)#0] DEBUG com.futures.watermark.IdleAwareWatermarkGenerator - Partition became idle (10121ms since last event), advancing watermark to maxTimestamp: -9223372036854775307
2025-08-20 22:02:05.019 [Source: SingleLeg Orders Stream (1/4)#0] DEBUG com.futures.watermark.IdleAwareWatermarkGenerator - Partition became idle (10121ms since last event), advancing watermark to maxTimestamp: -9223372036854775307
2025-08-20 22:02:05.034 [Source: Combo Orders Stream (4/4)#0] DEBUG com.futures.watermark.IdleAwareWatermarkGenerator - Partition became idle (10136ms since last event), advancing watermark to maxTimestamp: -9223372036854775307
2025-08-20 22:02:05.034 [Source: Combo Orders Stream (2/4)#0] DEBUG com.futures.watermark.IdleAwareWatermarkGenerator - Partition became idle (10136ms since last event), advancing watermark to maxTimestamp: -9223372036854775307
2025-08-20 22:02:05.034 [Source: Combo Orders Stream (1/4)#0] DEBUG com.futures.watermark.IdleAwareWatermarkGenerator - Partition became idle (10136ms since last event), advancing watermark to maxTimestamp: -9223372036854775307
2025-08-20 22:02:05.034 [Source: SingleLeg Orders Stream (2/4)#0] DEBUG com.futures.watermark.IdleAwareWatermarkGenerator - Partition became idle (10136ms since last event), advancing watermark to maxTimestamp: -9223372036854775307
2025-08-20 22:02:05.034 [Source: SingleLeg Orders Stream (3/4)#0] DEBUG com.futures.watermark.IdleAwareWatermarkGenerator - Partition became idle (10136ms since last event), advancing watermark to maxTimestamp: -9223372036854775307
2025-08-20 22:02:05.050 [Source: Combo Orders Stream (3/4)#0] DEBUG com.futures.watermark.IdleAwareWatermarkGenerator - Partition became idle (10152ms since last event), advancing watermark to maxTimestamp: -9223372036854775307
2025-08-20 22:02:05.453 [Source: SingleLeg Orders Stream (2/4)#0] DEBUG com.futures.watermark.IdleAwareWatermarkGenerator - Partition became idle (10116ms since last event), advancing watermark to maxTimestamp: 1755698283224
2025-08-20 22:02:05.453 [Source: Combo Orders Stream (4/4)#0] DEBUG com.futures.watermark.IdleAwareWatermarkGenerator - Partition became idle (10131ms since last event), advancing watermark to maxTimestamp: 1755698286202
2025-08-20 22:04:06.621 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (1/4)#0] INFO  com.futures.function.VirtualOrderBookBuilder - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (1/4)#0
2025-08-20 22:04:06.621 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] INFO  com.futures.function.BaseOrderBookBuilder - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0
2025-08-20 22:04:06.621 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#0] INFO  com.futures.function.BaseOrderBookBuilder - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#0
2025-08-20 22:04:06.621 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (2/4)#0] INFO  com.futures.function.VirtualOrderBookBuilder - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (2/4)#0
2025-08-20 22:04:06.622 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (3/4)#0] INFO  com.futures.function.BaseOrderBookBuilder - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (3/4)#0
2025-08-20 22:04:06.621 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (3/4)#0] INFO  com.futures.function.VirtualOrderBookBuilder - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (3/4)#0
2025-08-20 22:04:06.621 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] INFO  com.futures.function.VirtualOrderBookBuilder - VirtualOrderBookBuilder opened for task: Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0
2025-08-20 22:04:06.621 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] INFO  com.futures.function.BaseOrderBookBuilder - BaseOrderBookBuilder opened for task: Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0
2025-08-20 22:04:06.785 [Source: SingleLeg Orders Stream (2/4)#0] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 22:04:06.785 [Source: Combo Orders Stream (3/4)#0] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 22:04:06.785 [Source: Combo Orders Stream (4/4)#0] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 22:04:06.785 [Source: Combo Orders Stream (1/4)#0] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 22:04:06.785 [Source: Combo Orders Stream (2/4)#0] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 22:04:06.786 [Source: SingleLeg Orders Stream (4/4)#0] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 22:04:06.785 [Source: SingleLeg Orders Stream (1/4)#0] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 22:04:06.785 [Source: SingleLeg Orders Stream (3/4)#0] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 22:04:06.944 [Source: SingleLeg Orders Stream (2/4)#0] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 22:04:06.944 [Source: Combo Orders Stream (4/4)#0] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 22:04:07.221 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] DEBUG com.futures.function.VirtualOrderBookBuilder - Processing combo order for EB2504-EB2505: 100257414
2025-08-20 22:04:07.232 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Combo order has invalid BS tag: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=13, timestamp=1755698284349}
2025-08-20 22:04:07.233 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Invalid combo order received: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=13, timestamp=1755698284349}
2025-08-20 22:04:07.233 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] DEBUG com.futures.function.VirtualOrderBookBuilder - Processing combo order for EB2504-EB2505: 100257414
2025-08-20 22:04:07.233 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Combo order has invalid BS tag: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=12, timestamp=1755698284591}
2025-08-20 22:04:07.233 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Invalid combo order received: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=12, timestamp=1755698284591}
2025-08-20 22:04:07.233 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] DEBUG com.futures.function.VirtualOrderBookBuilder - Processing combo order for EB2504-EB2505: 100257414
2025-08-20 22:04:07.233 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Combo order has invalid BS tag: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=11, timestamp=1755698284713}
2025-08-20 22:04:07.233 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Invalid combo order received: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=11, timestamp=1755698284713}
2025-08-20 22:04:07.233 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] DEBUG com.futures.function.VirtualOrderBookBuilder - Processing combo order for EB2504-EB2505: 100257414
2025-08-20 22:04:07.233 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Combo order has invalid BS tag: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=10, timestamp=1755698284839}
2025-08-20 22:04:07.233 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Invalid combo order received: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=10, timestamp=1755698284839}
2025-08-20 22:04:07.233 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] DEBUG com.futures.function.VirtualOrderBookBuilder - Processing combo order for EB2504-EB2505: 100257414
2025-08-20 22:04:07.233 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Combo order has invalid BS tag: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=9, timestamp=1755698284962}
2025-08-20 22:04:07.233 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Invalid combo order received: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=9, timestamp=1755698284962}
2025-08-20 22:04:07.233 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] DEBUG com.futures.function.VirtualOrderBookBuilder - Processing combo order for EB2504-EB2505: 100257414
2025-08-20 22:04:07.234 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Combo order has invalid BS tag: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=8, timestamp=1755698285086}
2025-08-20 22:04:07.234 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Invalid combo order received: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=8, timestamp=1755698285086}
2025-08-20 22:04:07.234 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] DEBUG com.futures.function.VirtualOrderBookBuilder - Processing combo order for EB2504-EB2505: 100257414
2025-08-20 22:04:07.234 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Combo order has invalid BS tag: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=7, timestamp=1755698285210}
2025-08-20 22:04:07.234 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Invalid combo order received: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=7, timestamp=1755698285210}
2025-08-20 22:04:07.234 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] DEBUG com.futures.function.VirtualOrderBookBuilder - Processing combo order for EB2504-EB2505: 100257414
2025-08-20 22:04:07.234 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Combo order has invalid BS tag: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=6, timestamp=1755698285335}
2025-08-20 22:04:07.234 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Invalid combo order received: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=6, timestamp=1755698285335}
2025-08-20 22:04:07.234 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] DEBUG com.futures.function.VirtualOrderBookBuilder - Processing combo order for EB2504-EB2505: 100257414
2025-08-20 22:04:07.234 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Combo order has invalid BS tag: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=4, timestamp=1755698285459}
2025-08-20 22:04:07.234 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Invalid combo order received: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=4, timestamp=1755698285459}
2025-08-20 22:04:07.234 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] DEBUG com.futures.function.VirtualOrderBookBuilder - Processing combo order for EB2504-EB2505: 100257414
2025-08-20 22:04:07.234 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Combo order has invalid BS tag: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698285583}
2025-08-20 22:04:07.234 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Invalid combo order received: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698285583}
2025-08-20 22:04:07.234 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] DEBUG com.futures.function.VirtualOrderBookBuilder - Processing combo order for EB2504-EB2505: 100257414
2025-08-20 22:04:07.234 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Combo order has invalid BS tag: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698285707}
2025-08-20 22:04:07.234 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Invalid combo order received: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698285707}
2025-08-20 22:04:07.234 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] DEBUG com.futures.function.VirtualOrderBookBuilder - Processing combo order for EB2504-EB2505: 100257414
2025-08-20 22:04:07.234 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Combo order has invalid BS tag: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698285831}
2025-08-20 22:04:07.235 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Invalid combo order received: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698285831}
2025-08-20 22:04:07.235 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] DEBUG com.futures.function.VirtualOrderBookBuilder - Processing combo order for EB2504-EB2505: 100257400
2025-08-20 22:04:07.235 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Combo order has invalid BS tag: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257400', bSTag='null', trdPrc=0.0, rmnVol=4, timestamp=1755698285954}
2025-08-20 22:04:07.235 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Invalid combo order received: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257400', bSTag='null', trdPrc=0.0, rmnVol=4, timestamp=1755698285954}
2025-08-20 22:04:07.235 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] DEBUG com.futures.function.VirtualOrderBookBuilder - Processing combo order for EB2504-EB2505: 100257401
2025-08-20 22:04:07.235 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Combo order has invalid BS tag: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257401', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698286078}
2025-08-20 22:04:07.235 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Invalid combo order received: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257401', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698286078}
2025-08-20 22:04:07.235 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] DEBUG com.futures.function.VirtualOrderBookBuilder - Processing combo order for EB2504-EB2505: 100257400
2025-08-20 22:04:07.235 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Combo order has invalid BS tag: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257400', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698286202}
2025-08-20 22:04:07.235 [Virtual OrderBook Builder -> Sink: Virtual OrderBook Sink (4/4)#0] WARN  com.futures.function.VirtualOrderBookBuilder - Invalid combo order received: ComboOrder{comboId='EB2504-EB2505', leg1ContractCde='EB2504', leg2ContractCde='EB2505', ordNbr='100257400', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698286202}
2025-08-20 22:04:07.237 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract JD2504-C-3200: 10000000
2025-08-20 22:04:07.237 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract SH2505: 10000021
2025-08-20 22:04:07.237 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract CS2505-C-2750: 10000017
2025-08-20 22:04:07.245 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='CS2505-C-2750', ordNbr='10000017', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698275405, expiryTimestamp=0}
2025-08-20 22:04:07.245 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='SH2505', ordNbr='10000021', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698276773, expiryTimestamp=0}
2025-08-20 22:04:07.245 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='CS2505-C-2750', ordNbr='10000017', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698275405, expiryTimestamp=0}
2025-08-20 22:04:07.245 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000000', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698273857, expiryTimestamp=0}
2025-08-20 22:04:07.245 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='SH2505', ordNbr='10000021', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698276773, expiryTimestamp=0}
2025-08-20 22:04:07.245 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000000', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698273857, expiryTimestamp=0}
2025-08-20 22:04:07.245 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract CS2505-C-2750: 10000017
2025-08-20 22:04:07.245 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract SH2505: 10000021
2025-08-20 22:04:07.245 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract JD2504-C-3200: 10000001
2025-08-20 22:04:07.245 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='CS2505-C-2750', ordNbr='10000017', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698275529, expiryTimestamp=0}
2025-08-20 22:04:07.245 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='SH2505', ordNbr='10000021', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698276895, expiryTimestamp=0}
2025-08-20 22:04:07.245 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000001', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698274413, expiryTimestamp=0}
2025-08-20 22:04:07.245 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='CS2505-C-2750', ordNbr='10000017', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698275529, expiryTimestamp=0}
2025-08-20 22:04:07.245 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000001', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698274413, expiryTimestamp=0}
2025-08-20 22:04:07.245 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='SH2505', ordNbr='10000021', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698276895, expiryTimestamp=0}
2025-08-20 22:04:07.245 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract CS2505-C-2750: 10000019
2025-08-20 22:04:07.245 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract JD2504-C-3200: 10000001
2025-08-20 22:04:07.245 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='CS2505-C-2750', ordNbr='10000019', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698275652, expiryTimestamp=0}
2025-08-20 22:04:07.245 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract SH2505: 10000023
2025-08-20 22:04:07.245 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='CS2505-C-2750', ordNbr='10000019', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698275652, expiryTimestamp=0}
2025-08-20 22:04:07.245 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000001', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698274535, expiryTimestamp=0}
2025-08-20 22:04:07.245 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='SH2505', ordNbr='10000023', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698277018, expiryTimestamp=0}
2025-08-20 22:04:07.245 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract CS2505-C-2750: 10000019
2025-08-20 22:04:07.245 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='SH2505', ordNbr='10000023', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698277018, expiryTimestamp=0}
2025-08-20 22:04:07.246 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='CS2505-C-2750', ordNbr='10000019', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698275776, expiryTimestamp=0}
2025-08-20 22:04:07.245 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000001', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698274535, expiryTimestamp=0}
2025-08-20 22:04:07.246 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='CS2505-C-2750', ordNbr='10000019', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698275776, expiryTimestamp=0}
2025-08-20 22:04:07.246 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract SH2505: 10000023
2025-08-20 22:04:07.246 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract JD2504-C-3200: 10000005
2025-08-20 22:04:07.246 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100257097
2025-08-20 22:04:07.246 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='SH2505', ordNbr='10000023', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698277144, expiryTimestamp=0}
2025-08-20 22:04:07.246 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000005', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698274659, expiryTimestamp=0}
2025-08-20 22:04:07.246 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000005', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698274659, expiryTimestamp=0}
2025-08-20 22:04:07.246 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (1/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='SH2505', ordNbr='10000023', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698277144, expiryTimestamp=0}
2025-08-20 22:04:07.246 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257097', bSTag='null', trdPrc=0.0, rmnVol=10, timestamp=1755698276027, expiryTimestamp=0}
2025-08-20 22:04:07.246 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257097', bSTag='null', trdPrc=0.0, rmnVol=10, timestamp=1755698276027, expiryTimestamp=0}
2025-08-20 22:04:07.246 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract JD2504-C-3200: 10000005
2025-08-20 22:04:07.246 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100257007
2025-08-20 22:04:07.246 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000005', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698274783, expiryTimestamp=0}
2025-08-20 22:04:07.246 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257007', bSTag='null', trdPrc=0.0, rmnVol=10, timestamp=1755698276148, expiryTimestamp=0}
2025-08-20 22:04:07.246 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000005', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698274783, expiryTimestamp=0}
2025-08-20 22:04:07.246 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257007', bSTag='null', trdPrc=0.0, rmnVol=10, timestamp=1755698276148, expiryTimestamp=0}
2025-08-20 22:04:07.246 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract JD2504-C-3200: 10000009
2025-08-20 22:04:07.246 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100257007
2025-08-20 22:04:07.246 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000009', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698274910, expiryTimestamp=0}
2025-08-20 22:04:07.246 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257007', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698276273, expiryTimestamp=0}
2025-08-20 22:04:07.246 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000009', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698274910, expiryTimestamp=0}
2025-08-20 22:04:07.246 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257007', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698276273, expiryTimestamp=0}
2025-08-20 22:04:07.246 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract JD2504-C-3200: 10000009
2025-08-20 22:04:07.246 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100257004
2025-08-20 22:04:07.246 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000009', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698275032, expiryTimestamp=0}
2025-08-20 22:04:07.246 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257004', bSTag='null', trdPrc=0.0, rmnVol=10, timestamp=1755698276399, expiryTimestamp=0}
2025-08-20 22:04:07.246 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000009', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698275032, expiryTimestamp=0}
2025-08-20 22:04:07.246 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257004', bSTag='null', trdPrc=0.0, rmnVol=10, timestamp=1755698276399, expiryTimestamp=0}
2025-08-20 22:04:07.247 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100257004
2025-08-20 22:04:07.247 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract JD2504-C-3200: 10000011
2025-08-20 22:04:07.247 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257004', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698276524, expiryTimestamp=0}
2025-08-20 22:04:07.247 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000011', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698275155, expiryTimestamp=0}
2025-08-20 22:04:07.247 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257004', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698276524, expiryTimestamp=0}
2025-08-20 22:04:07.247 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000011', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698275155, expiryTimestamp=0}
2025-08-20 22:04:07.247 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100257098
2025-08-20 22:04:07.247 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract JD2504-C-3200: 10000011
2025-08-20 22:04:07.247 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257098', bSTag='null', trdPrc=0.0, rmnVol=3, timestamp=1755698276648, expiryTimestamp=0}
2025-08-20 22:04:07.247 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000011', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698275280, expiryTimestamp=0}
2025-08-20 22:04:07.247 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257098', bSTag='null', trdPrc=0.0, rmnVol=3, timestamp=1755698276648, expiryTimestamp=0}
2025-08-20 22:04:07.247 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000011', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698275280, expiryTimestamp=0}
2025-08-20 22:04:07.247 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2504: 100257000
2025-08-20 22:04:07.247 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257000', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698277268, expiryTimestamp=0}
2025-08-20 22:04:07.247 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract JD2504-C-3200: 10000000
2025-08-20 22:04:07.247 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257000', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698277268, expiryTimestamp=0}
2025-08-20 22:04:07.247 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000000', bSTag='null', trdPrc=0.0, rmnVol=3, timestamp=1755698275901, expiryTimestamp=0}
2025-08-20 22:04:07.247 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2504: 100257001
2025-08-20 22:04:07.247 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (2/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='JD2504-C-3200', ordNbr='10000000', bSTag='null', trdPrc=0.0, rmnVol=3, timestamp=1755698275901, expiryTimestamp=0}
2025-08-20 22:04:07.247 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257001', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698277392, expiryTimestamp=0}
2025-08-20 22:04:07.247 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257001', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698277392, expiryTimestamp=0}
2025-08-20 22:04:07.247 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2504: 100257002
2025-08-20 22:04:07.247 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257002', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698277516, expiryTimestamp=0}
2025-08-20 22:04:07.247 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257002', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698277516, expiryTimestamp=0}
2025-08-20 22:04:07.247 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2504: 100257003
2025-08-20 22:04:07.247 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257003', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698277644, expiryTimestamp=0}
2025-08-20 22:04:07.247 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257003', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698277644, expiryTimestamp=0}
2025-08-20 22:04:07.248 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100257009
2025-08-20 22:04:07.248 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257009', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698277766, expiryTimestamp=0}
2025-08-20 22:04:07.248 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257009', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698277766, expiryTimestamp=0}
2025-08-20 22:04:07.248 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100257005
2025-08-20 22:04:07.248 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257005', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698277891, expiryTimestamp=0}
2025-08-20 22:04:07.248 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257005', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698277891, expiryTimestamp=0}
2025-08-20 22:04:07.248 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100257006
2025-08-20 22:04:07.248 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257006', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698278013, expiryTimestamp=0}
2025-08-20 22:04:07.248 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257006', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698278013, expiryTimestamp=0}
2025-08-20 22:04:07.248 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100257008
2025-08-20 22:04:07.248 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257008', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698278136, expiryTimestamp=0}
2025-08-20 22:04:07.248 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257008', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698278136, expiryTimestamp=0}
2025-08-20 22:04:07.248 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100256966
2025-08-20 22:04:07.248 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100256966', bSTag='null', trdPrc=0.0, rmnVol=9, timestamp=1755698278259, expiryTimestamp=0}
2025-08-20 22:04:07.248 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100256966', bSTag='null', trdPrc=0.0, rmnVol=9, timestamp=1755698278259, expiryTimestamp=0}
2025-08-20 22:04:07.248 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100257085
2025-08-20 22:04:07.248 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257085', bSTag='null', trdPrc=0.0, rmnVol=8, timestamp=1755698278383, expiryTimestamp=0}
2025-08-20 22:04:07.248 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257085', bSTag='null', trdPrc=0.0, rmnVol=8, timestamp=1755698278383, expiryTimestamp=0}
2025-08-20 22:04:07.248 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100257085
2025-08-20 22:04:07.248 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257085', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698278507, expiryTimestamp=0}
2025-08-20 22:04:07.248 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257085', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698278507, expiryTimestamp=0}
2025-08-20 22:04:07.248 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100256966
2025-08-20 22:04:07.249 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100256966', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698278630, expiryTimestamp=0}
2025-08-20 22:04:07.249 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100256966', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698278630, expiryTimestamp=0}
2025-08-20 22:04:07.249 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100256966
2025-08-20 22:04:07.249 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100256966', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698278754, expiryTimestamp=0}
2025-08-20 22:04:07.249 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100256966', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698278754, expiryTimestamp=0}
2025-08-20 22:04:07.249 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100257259
2025-08-20 22:04:07.249 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257259', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698278878, expiryTimestamp=0}
2025-08-20 22:04:07.249 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257259', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698278878, expiryTimestamp=0}
2025-08-20 22:04:07.249 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100257259
2025-08-20 22:04:07.249 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257259', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698279003, expiryTimestamp=0}
2025-08-20 22:04:07.249 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257259', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698279003, expiryTimestamp=0}
2025-08-20 22:04:07.249 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100256966
2025-08-20 22:04:07.249 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100256966', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698279127, expiryTimestamp=0}
2025-08-20 22:04:07.249 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100256966', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698279127, expiryTimestamp=0}
2025-08-20 22:04:07.249 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100254367
2025-08-20 22:04:07.249 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100254367', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698279251, expiryTimestamp=0}
2025-08-20 22:04:07.249 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100254367', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698279251, expiryTimestamp=0}
2025-08-20 22:04:07.249 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100254367
2025-08-20 22:04:07.249 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100254367', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698279375, expiryTimestamp=0}
2025-08-20 22:04:07.249 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100254367', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698279375, expiryTimestamp=0}
2025-08-20 22:04:07.249 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2504: 100257479
2025-08-20 22:04:07.249 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257479', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698279498, expiryTimestamp=0}
2025-08-20 22:04:07.250 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257479', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698279498, expiryTimestamp=0}
2025-08-20 22:04:07.250 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2504: 100257479
2025-08-20 22:04:07.250 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257479', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698279623, expiryTimestamp=0}
2025-08-20 22:04:07.250 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257479', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698279623, expiryTimestamp=0}
2025-08-20 22:04:07.250 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2504: 100256192
2025-08-20 22:04:07.250 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100256192', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698279747, expiryTimestamp=0}
2025-08-20 22:04:07.250 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100256192', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698279747, expiryTimestamp=0}
2025-08-20 22:04:07.250 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2504: 100256192
2025-08-20 22:04:07.250 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100256192', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698279871, expiryTimestamp=0}
2025-08-20 22:04:07.250 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100256192', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698279871, expiryTimestamp=0}
2025-08-20 22:04:07.250 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100257507
2025-08-20 22:04:07.250 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257507', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698279995, expiryTimestamp=0}
2025-08-20 22:04:07.250 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257507', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698279995, expiryTimestamp=0}
2025-08-20 22:04:07.250 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100257507
2025-08-20 22:04:07.250 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257507', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698280120, expiryTimestamp=0}
2025-08-20 22:04:07.250 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257507', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698280120, expiryTimestamp=0}
2025-08-20 22:04:07.250 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100257466
2025-08-20 22:04:07.250 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257466', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698280243, expiryTimestamp=0}
2025-08-20 22:04:07.250 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257466', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698280243, expiryTimestamp=0}
2025-08-20 22:04:07.250 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100257466
2025-08-20 22:04:07.250 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257466', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698280367, expiryTimestamp=0}
2025-08-20 22:04:07.250 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257466', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698280367, expiryTimestamp=0}
2025-08-20 22:04:07.250 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100257500
2025-08-20 22:04:07.250 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257500', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698280490, expiryTimestamp=0}
2025-08-20 22:04:07.250 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257500', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698280490, expiryTimestamp=0}
2025-08-20 22:04:07.251 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100257500
2025-08-20 22:04:07.251 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257500', bSTag='null', trdPrc=0.0, rmnVol=3, timestamp=1755698280614, expiryTimestamp=0}
2025-08-20 22:04:07.251 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257500', bSTag='null', trdPrc=0.0, rmnVol=3, timestamp=1755698280614, expiryTimestamp=0}
2025-08-20 22:04:07.251 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2504: 100257560
2025-08-20 22:04:07.251 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257560', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698280737, expiryTimestamp=0}
2025-08-20 22:04:07.251 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257560', bSTag='null', trdPrc=0.0, rmnVol=5, timestamp=1755698280737, expiryTimestamp=0}
2025-08-20 22:04:07.251 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2504: 100257560
2025-08-20 22:04:07.251 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257560', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698280860, expiryTimestamp=0}
2025-08-20 22:04:07.251 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257560', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698280860, expiryTimestamp=0}
2025-08-20 22:04:07.251 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2504: 100257560
2025-08-20 22:04:07.251 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257560', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698280985, expiryTimestamp=0}
2025-08-20 22:04:07.251 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257560', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698280985, expiryTimestamp=0}
2025-08-20 22:04:07.251 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100257500
2025-08-20 22:04:07.251 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257500', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698281110, expiryTimestamp=0}
2025-08-20 22:04:07.251 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257500', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698281110, expiryTimestamp=0}
2025-08-20 22:04:07.251 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100257500
2025-08-20 22:04:07.252 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257500', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698281235, expiryTimestamp=0}
2025-08-20 22:04:07.252 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257500', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698281235, expiryTimestamp=0}
2025-08-20 22:04:07.252 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2504: 100257608
2025-08-20 22:04:07.252 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257608', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698281359, expiryTimestamp=0}
2025-08-20 22:04:07.252 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257608', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698281359, expiryTimestamp=0}
2025-08-20 22:04:07.252 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2504: 100257608
2025-08-20 22:04:07.252 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257608', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698281482, expiryTimestamp=0}
2025-08-20 22:04:07.252 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257608', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698281482, expiryTimestamp=0}
2025-08-20 22:04:07.252 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2504: 100257648
2025-08-20 22:04:07.252 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257648', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698281607, expiryTimestamp=0}
2025-08-20 22:04:07.252 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257648', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698281607, expiryTimestamp=0}
2025-08-20 22:04:07.253 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2504: 100257648
2025-08-20 22:04:07.253 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257648', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698281732, expiryTimestamp=0}
2025-08-20 22:04:07.253 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257648', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698281732, expiryTimestamp=0}
2025-08-20 22:04:07.253 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2504: 100257648
2025-08-20 22:04:07.253 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257648', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698281857, expiryTimestamp=0}
2025-08-20 22:04:07.253 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257648', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698281857, expiryTimestamp=0}
2025-08-20 22:04:07.253 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100257736
2025-08-20 22:04:07.253 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257736', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698281981, expiryTimestamp=0}
2025-08-20 22:04:07.253 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257736', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698281981, expiryTimestamp=0}
2025-08-20 22:04:07.253 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100257736
2025-08-20 22:04:07.253 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100257736', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698282105, expiryTimestamp=0}
2025-08-20 22:04:07.253 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100257736', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698282105, expiryTimestamp=0}
2025-08-20 22:04:07.253 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2504: 100257414
2025-08-20 22:04:07.253 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=10, timestamp=1755698282229, expiryTimestamp=0}
2025-08-20 22:04:07.253 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=10, timestamp=1755698282229, expiryTimestamp=0}
2025-08-20 22:04:07.253 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2504: 100257414
2025-08-20 22:04:07.253 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698282353, expiryTimestamp=0}
2025-08-20 22:04:07.253 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257414', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698282353, expiryTimestamp=0}
2025-08-20 22:04:07.253 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2504: 100257342
2025-08-20 22:04:07.253 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257342', bSTag='null', trdPrc=0.0, rmnVol=10, timestamp=1755698282477, expiryTimestamp=0}
2025-08-20 22:04:07.253 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257342', bSTag='null', trdPrc=0.0, rmnVol=10, timestamp=1755698282477, expiryTimestamp=0}
2025-08-20 22:04:07.253 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2504: 100257342
2025-08-20 22:04:07.253 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257342', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698282602, expiryTimestamp=0}
2025-08-20 22:04:07.253 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257342', bSTag='null', trdPrc=0.0, rmnVol=0, timestamp=1755698282602, expiryTimestamp=0}
2025-08-20 22:04:07.253 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100258082
2025-08-20 22:04:07.253 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100258082', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698282727, expiryTimestamp=0}
2025-08-20 22:04:07.253 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100258082', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698282727, expiryTimestamp=0}
2025-08-20 22:04:07.254 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100258082
2025-08-20 22:04:07.254 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100258082', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698282851, expiryTimestamp=0}
2025-08-20 22:04:07.254 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100258082', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698282851, expiryTimestamp=0}
2025-08-20 22:04:07.254 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100258081
2025-08-20 22:04:07.254 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100258081', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698282976, expiryTimestamp=0}
2025-08-20 22:04:07.254 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100258081', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698282976, expiryTimestamp=0}
2025-08-20 22:04:07.254 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2505: 100258083
2025-08-20 22:04:07.254 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2505', ordNbr='100258083', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698283100, expiryTimestamp=0}
2025-08-20 22:04:07.254 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2505', ordNbr='100258083', bSTag='null', trdPrc=0.0, rmnVol=2, timestamp=1755698283100, expiryTimestamp=0}
2025-08-20 22:04:07.254 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] DEBUG com.futures.function.BaseOrderBookBuilder - Processing order for contract EB2504: 100257349
2025-08-20 22:04:07.254 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Order has invalid BS tag: SingleLegOrder{contractCde='EB2504', ordNbr='100257349', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698283224, expiryTimestamp=0}
2025-08-20 22:04:07.254 [Base OrderBook Builder -> (Sink: Base OrderBook Sink, Sink: BBO Update Sink) (4/4)#0] WARN  com.futures.function.BaseOrderBookBuilder - Invalid order received: SingleLegOrder{contractCde='EB2504', ordNbr='100257349', bSTag='null', trdPrc=0.0, rmnVol=1, timestamp=1755698283224, expiryTimestamp=0}
2025-08-20 22:04:16.900 [Source: SingleLeg Orders Stream (2/4)#0] DEBUG com.futures.watermark.IdleAwareWatermarkGenerator - Partition became idle (10115ms since last event), advancing watermark to maxTimestamp: -9223372036854775307
2025-08-20 22:04:16.900 [Source: SingleLeg Orders Stream (3/4)#0] DEBUG com.futures.watermark.IdleAwareWatermarkGenerator - Partition became idle (10115ms since last event), advancing watermark to maxTimestamp: -9223372036854775307
2025-08-20 22:04:16.900 [Source: SingleLeg Orders Stream (1/4)#0] DEBUG com.futures.watermark.IdleAwareWatermarkGenerator - Partition became idle (10115ms since last event), advancing watermark to maxTimestamp: -9223372036854775307
2025-08-20 22:04:16.900 [Source: Combo Orders Stream (4/4)#0] DEBUG com.futures.watermark.IdleAwareWatermarkGenerator - Partition became idle (10115ms since last event), advancing watermark to maxTimestamp: -9223372036854775307
2025-08-20 22:04:16.900 [Source: Combo Orders Stream (2/4)#0] DEBUG com.futures.watermark.IdleAwareWatermarkGenerator - Partition became idle (10115ms since last event), advancing watermark to maxTimestamp: -9223372036854775307
2025-08-20 22:04:16.900 [Source: Combo Orders Stream (1/4)#0] DEBUG com.futures.watermark.IdleAwareWatermarkGenerator - Partition became idle (10115ms since last event), advancing watermark to maxTimestamp: -9223372036854775307
2025-08-20 22:04:16.900 [Source: SingleLeg Orders Stream (4/4)#0] DEBUG com.futures.watermark.IdleAwareWatermarkGenerator - Partition became idle (10114ms since last event), advancing watermark to maxTimestamp: -9223372036854775307
2025-08-20 22:04:16.900 [Source: Combo Orders Stream (3/4)#0] DEBUG com.futures.watermark.IdleAwareWatermarkGenerator - Partition became idle (10115ms since last event), advancing watermark to maxTimestamp: -9223372036854775307
2025-08-20 22:04:17.305 [Source: Combo Orders Stream (4/4)#0] DEBUG com.futures.watermark.IdleAwareWatermarkGenerator - Partition became idle (10129ms since last event), advancing watermark to maxTimestamp: 1755698286202
2025-08-20 22:04:17.305 [Source: SingleLeg Orders Stream (2/4)#0] DEBUG com.futures.watermark.IdleAwareWatermarkGenerator - Partition became idle (10115ms since last event), advancing watermark to maxTimestamp: 1755698283224
