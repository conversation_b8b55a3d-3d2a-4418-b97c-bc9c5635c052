2025-08-20 21:42:21.085 [Source: Combo Orders Stream (2/4)#0] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:21.085 [Source: Combo Orders Stream (4/4)#0] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:21.085 [Source: Combo Orders Stream (1/4)#0] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:21.085 [Source: SingleLeg Orders Stream (1/4)#0] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:21.085 [Source: Combo Orders Stream (3/4)#0] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:21.085 [Source: SingleLeg Orders Stream (3/4)#0] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:21.085 [Source: SingleLeg Orders Stream (4/4)#0] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:21.085 [Source: SingleLeg Orders Stream (2/4)#0] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:24.181 [Source: SingleLeg Orders Stream (1/4)#1] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:24.187 [Source: SingleLeg Orders Stream (3/4)#1] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:24.188 [Source: SingleLeg Orders Stream (2/4)#1] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:24.191 [Source: SingleLeg Orders Stream (4/4)#1] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:24.195 [Source: Combo Orders Stream (1/4)#1] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:24.197 [Source: Combo Orders Stream (3/4)#1] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:24.198 [Source: Combo Orders Stream (2/4)#1] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:24.203 [Source: Combo Orders Stream (4/4)#1] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:27.458 [Source: SingleLeg Orders Stream (1/4)#2] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:27.462 [Source: SingleLeg Orders Stream (2/4)#2] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:27.462 [Source: Combo Orders Stream (1/4)#2] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:27.464 [Source: SingleLeg Orders Stream (4/4)#2] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:27.464 [Source: SingleLeg Orders Stream (3/4)#2] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:27.464 [Source: Combo Orders Stream (2/4)#2] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:27.485 [Source: Combo Orders Stream (4/4)#2] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:27.500 [Source: Combo Orders Stream (3/4)#2] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:30.637 [Source: SingleLeg Orders Stream (2/4)#3] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:30.639 [Source: SingleLeg Orders Stream (1/4)#3] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:30.642 [Source: SingleLeg Orders Stream (3/4)#3] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:30.644 [Source: SingleLeg Orders Stream (4/4)#3] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:30.646 [Source: Combo Orders Stream (2/4)#3] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:30.646 [Source: Combo Orders Stream (1/4)#3] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:30.651 [Source: Combo Orders Stream (3/4)#3] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:42:30.652 [Source: Combo Orders Stream (4/4)#3] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:20.007 [Source: Combo Orders Stream (3/4)#0] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:20.007 [Source: SingleLeg Orders Stream (3/4)#0] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:20.007 [Source: Combo Orders Stream (1/4)#0] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:20.007 [Source: SingleLeg Orders Stream (4/4)#0] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:20.007 [Source: Combo Orders Stream (4/4)#0] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:20.007 [Source: SingleLeg Orders Stream (1/4)#0] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:20.007 [Source: SingleLeg Orders Stream (2/4)#0] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:20.007 [Source: Combo Orders Stream (2/4)#0] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:34.246 [Source: SingleLeg Orders Stream (1/4)#1] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:34.246 [Source: SingleLeg Orders Stream (2/4)#1] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:34.251 [Source: SingleLeg Orders Stream (3/4)#1] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:34.255 [Source: SingleLeg Orders Stream (4/4)#1] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:34.256 [Source: Combo Orders Stream (1/4)#1] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:34.260 [Source: Combo Orders Stream (2/4)#1] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:34.262 [Source: Combo Orders Stream (3/4)#1] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:34.282 [Source: Combo Orders Stream (4/4)#1] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:34.288 [Source: SingleLeg Orders Stream (2/4)#1] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:34.288 [Source: Combo Orders Stream (4/4)#1] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:57.317 [Source: SingleLeg Orders Stream (3/4)#2] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:57.317 [Source: SingleLeg Orders Stream (2/4)#2] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:57.318 [Source: SingleLeg Orders Stream (1/4)#2] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:57.324 [Source: SingleLeg Orders Stream (4/4)#2] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:57.325 [Source: Combo Orders Stream (1/4)#2] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:57.334 [Source: Combo Orders Stream (3/4)#2] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:57.334 [Source: Combo Orders Stream (2/4)#2] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:57.344 [Source: Combo Orders Stream (4/4)#2] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:57.349 [Source: SingleLeg Orders Stream (2/4)#2] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:58:57.349 [Source: Combo Orders Stream (4/4)#2] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:13.383 [Source: SingleLeg Orders Stream (1/4)#3] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:13.385 [Source: SingleLeg Orders Stream (3/4)#3] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:13.385 [Source: SingleLeg Orders Stream (2/4)#3] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:13.385 [Source: SingleLeg Orders Stream (4/4)#3] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:13.389 [Source: Combo Orders Stream (1/4)#3] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:13.391 [Source: Combo Orders Stream (2/4)#3] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:13.394 [Source: Combo Orders Stream (4/4)#3] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:13.394 [Source: Combo Orders Stream (3/4)#3] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:13.404 [Source: SingleLeg Orders Stream (2/4)#3] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:13.405 [Source: Combo Orders Stream (4/4)#3] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:30.159 [Source: SingleLeg Orders Stream (2/4)#4] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:30.159 [Source: SingleLeg Orders Stream (1/4)#4] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:30.160 [Source: SingleLeg Orders Stream (3/4)#4] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:30.163 [Source: SingleLeg Orders Stream (4/4)#4] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:30.165 [Source: Combo Orders Stream (1/4)#4] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:30.166 [Source: Combo Orders Stream (2/4)#4] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:30.168 [Source: Combo Orders Stream (3/4)#4] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:30.170 [Source: Combo Orders Stream (4/4)#4] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:30.180 [Source: SingleLeg Orders Stream (2/4)#4] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:30.180 [Source: Combo Orders Stream (4/4)#4] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:44.732 [Source: SingleLeg Orders Stream (2/4)#5] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:44.732 [Source: SingleLeg Orders Stream (3/4)#5] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:44.733 [Source: SingleLeg Orders Stream (1/4)#5] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:44.735 [Source: SingleLeg Orders Stream (4/4)#5] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:44.737 [Source: Combo Orders Stream (1/4)#5] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:44.739 [Source: Combo Orders Stream (2/4)#5] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:44.741 [Source: Combo Orders Stream (3/4)#5] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:44.741 [Source: Combo Orders Stream (4/4)#5] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:44.748 [Source: SingleLeg Orders Stream (2/4)#5] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:44.748 [Source: Combo Orders Stream (4/4)#5] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:57.694 [Source: SingleLeg Orders Stream (1/4)#6] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:57.695 [Source: SingleLeg Orders Stream (3/4)#6] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:57.695 [Source: SingleLeg Orders Stream (2/4)#6] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:57.696 [Source: SingleLeg Orders Stream (4/4)#6] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:57.699 [Source: Combo Orders Stream (1/4)#6] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:57.701 [Source: Combo Orders Stream (2/4)#6] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:57.703 [Source: Combo Orders Stream (4/4)#6] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:57.704 [Source: Combo Orders Stream (3/4)#6] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:57.714 [Source: SingleLeg Orders Stream (2/4)#6] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 21:59:57.714 [Source: Combo Orders Stream (4/4)#6] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 22:00:10.571 [Source: SingleLeg Orders Stream (1/4)#7] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 22:00:10.571 [Source: SingleLeg Orders Stream (2/4)#7] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 22:00:10.572 [Source: SingleLeg Orders Stream (3/4)#7] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 22:00:10.573 [Source: SingleLeg Orders Stream (4/4)#7] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 22:00:10.578 [Source: Combo Orders Stream (3/4)#7] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 22:00:10.579 [Source: Combo Orders Stream (2/4)#7] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 22:00:10.579 [Source: Combo Orders Stream (1/4)#7] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 22:00:10.580 [Source: Combo Orders Stream (4/4)#7] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 22:00:10.595 [Source: SingleLeg Orders Stream (2/4)#7] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
2025-08-20 22:00:10.596 [Source: Combo Orders Stream (4/4)#7] INFO  com.futures.watermark.IdleAwareWatermarkGenerator - IdleAwareWatermarkGenerator initialized: maxOutOfOrderness=500ms, idleTimeout=10000ms
