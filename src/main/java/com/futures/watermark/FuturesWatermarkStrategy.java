package com.futures.watermark;

import com.futures.model.SingleLegOrder;
import com.futures.model.ComboOrder;
import org.apache.flink.api.common.eventtime.TimestampAssigner;
import org.apache.flink.api.common.eventtime.TimestampAssignerSupplier;
import org.apache.flink.api.common.eventtime.WatermarkGenerator;
import org.apache.flink.api.common.eventtime.WatermarkGeneratorSupplier;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;

import java.time.Duration;

/**
 * 期货系统水印策略工厂
 * 
 * 提供针对期货交易数据的专用水印策略，包括：
 * 1. 带空闲检测的水印生成器
 * 2. 针对不同数据类型的时间戳提取器
 * 3. 可配置的乱序容忍度和空闲检测参数
 * 
 * <AUTHOR> System Team
 * @version 1.0
 */
public class FuturesWatermarkStrategy {
    
    /** 默认最大乱序容忍度 */
    public static final Duration DEFAULT_MAX_OUT_OF_ORDERNESS = Duration.ofMillis(500);
    
    /** 默认空闲超时时间 */
    public static final Duration DEFAULT_IDLE_TIMEOUT = Duration.ofSeconds(10);
    
    /**
     * 为单腿委托创建水印策略
     * 
     * @param maxOutOfOrderness 最大乱序容忍度
     * @param idleTimeout 空闲超时时间
     * @return 单腿委托的水印策略
     */
    public static WatermarkStrategy<SingleLegOrder> forSingleLegOrders(
            Duration maxOutOfOrderness, Duration idleTimeout) {
        
        return WatermarkStrategy
                .<SingleLegOrder>forGenerator(new WatermarkGeneratorSupplier<SingleLegOrder>() {
                    @Override
                    public WatermarkGenerator<SingleLegOrder> createWatermarkGenerator(Context context) {
                        return new IdleAwareWatermarkGenerator<>(maxOutOfOrderness, idleTimeout);
                    }
                })
                .withTimestampAssigner(new TimestampAssignerSupplier<SingleLegOrder>() {
                    @Override
                    public TimestampAssigner<SingleLegOrder> createTimestampAssigner(Context context) {
                        return new SingleLegOrderTimestampAssigner();
                    }
                });
    }
    
    /**
     * 为单腿委托创建默认水印策略
     */
    public static WatermarkStrategy<SingleLegOrder> forSingleLegOrders() {
        return forSingleLegOrders(DEFAULT_MAX_OUT_OF_ORDERNESS, DEFAULT_IDLE_TIMEOUT);
    }
    
    /**
     * 为组合委托创建水印策略
     * 
     * @param maxOutOfOrderness 最大乱序容忍度
     * @param idleTimeout 空闲超时时间
     * @return 组合委托的水印策略
     */
    public static WatermarkStrategy<ComboOrder> forComboOrders(
            Duration maxOutOfOrderness, Duration idleTimeout) {
        
        return WatermarkStrategy
                .<ComboOrder>forGenerator(new WatermarkGeneratorSupplier<ComboOrder>() {
                    @Override
                    public WatermarkGenerator<ComboOrder> createWatermarkGenerator(Context context) {
                        return new IdleAwareWatermarkGenerator<>(maxOutOfOrderness, idleTimeout);
                    }
                })
                .withTimestampAssigner(new TimestampAssignerSupplier<ComboOrder>() {
                    @Override
                    public TimestampAssigner<ComboOrder> createTimestampAssigner(Context context) {
                        return new ComboOrderTimestampAssigner();
                    }
                });
    }
    
    /**
     * 为组合委托创建默认水印策略
     */
    public static WatermarkStrategy<ComboOrder> forComboOrders() {
        return forComboOrders(DEFAULT_MAX_OUT_OF_ORDERNESS, DEFAULT_IDLE_TIMEOUT);
    }
    
    /**
     * 单腿委托时间戳提取器
     */
    public static class SingleLegOrderTimestampAssigner implements TimestampAssigner<SingleLegOrder> {
        
        @Override
        public long extractTimestamp(SingleLegOrder order, long recordTimestamp) {
            // 优先使用事件自带的时间戳，如果没有则使用记录时间戳
            long eventTimestamp = order.getTimestamp();
            if (eventTimestamp > 0) {
                return eventTimestamp;
            }
            
            // 如果事件时间戳无效，使用记录时间戳
            if (recordTimestamp > 0) {
                return recordTimestamp;
            }
            
            // 最后兜底使用当前系统时间
            return System.currentTimeMillis();
        }
    }
    
    /**
     * 组合委托时间戳提取器
     */
    public static class ComboOrderTimestampAssigner implements TimestampAssigner<ComboOrder> {
        
        @Override
        public long extractTimestamp(ComboOrder order, long recordTimestamp) {
            // 优先使用事件自带的时间戳，如果没有则使用记录时间戳
            long eventTimestamp = order.getTimestamp();
            if (eventTimestamp > 0) {
                return eventTimestamp;
            }
            
            // 如果事件时间戳无效，使用记录时间戳
            if (recordTimestamp > 0) {
                return recordTimestamp;
            }
            
            // 最后兜底使用当前系统时间
            return System.currentTimeMillis();
        }
    }
    
    /**
     * 创建高频交易场景的水印策略
     * 适用于高频交易，使用更小的乱序容忍度和空闲超时
     */
    public static WatermarkStrategy<SingleLegOrder> forHighFrequencyTrading() {
        return forSingleLegOrders(Duration.ofMillis(100), Duration.ofSeconds(5));
    }
    
    /**
     * 创建低延迟场景的水印策略
     * 适用于对延迟敏感的场景，使用最小的乱序容忍度
     */
    public static WatermarkStrategy<SingleLegOrder> forLowLatency() {
        return forSingleLegOrders(Duration.ofMillis(50), Duration.ofSeconds(3));
    }
    
    /**
     * 创建容错场景的水印策略
     * 适用于网络不稳定的环境，使用更大的乱序容忍度
     */
    public static WatermarkStrategy<SingleLegOrder> forFaultTolerant() {
        return forSingleLegOrders(Duration.ofSeconds(2), Duration.ofSeconds(30));
    }
}
