package com.futures.watermark;

import org.apache.flink.api.common.eventtime.Watermark;
import org.apache.flink.api.common.eventtime.WatermarkGenerator;
import org.apache.flink.api.common.eventtime.WatermarkOutput;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;

/**
 * 带空闲检测的自定义水印生成器
 * 
 * 这个水印生成器能够检测数据流空闲并自动推进水印，防止整个 Flink 作业的事件时间停滞。
 * 当一个分区在指定时间内没有收到新事件时，它会主动推进水印至当前分区的最大时间戳。
 * 
 * 核心特性：
 * 1. 跟踪分区的最大事件时间戳
 * 2. 检测分区空闲状态
 * 3. 在空闲时主动推进水印，避免全局水印停滞
 * 4. 支持可配置的乱序容忍度和空闲超时阈值
 * 
 * @param <T> 事件类型
 * <AUTHOR> System Team
 * @version 1.0
 */
public class IdleAwareWatermarkGenerator<T> implements WatermarkGenerator<T> {
    
    private static final Logger logger = LoggerFactory.getLogger(IdleAwareWatermarkGenerator.class);
    
    /** 最大乱序容忍度 (毫秒) */
    private final long maxOutOfOrderness;
    
    /** 空闲超时阈值 (毫秒) */
    private final long idleTimeout;
    
    /** 当前分区见过的最大事件时间戳 */
    private long maxTimestamp;
    
    /** 上一个事件到达时的处理时间 (wall-clock time) */
    private long lastEventTime;
    
    /** 上一次发射的水印值 */
    private long lastEmittedWatermark;
    
    /** 是否已标记为空闲状态 */
    private boolean isIdle;
    
    /** 事件计数器 */
    private long eventCount;
    
    /** 水印发射计数器 */
    private long watermarkCount;
    
    /**
     * 构造函数
     * 
     * @param maxOutOfOrderness 最大乱序容忍度
     * @param idleTimeout 空闲超时阈值
     */
    public IdleAwareWatermarkGenerator(Duration maxOutOfOrderness, Duration idleTimeout) {
        this.maxOutOfOrderness = maxOutOfOrderness.toMillis();
        this.idleTimeout = idleTimeout.toMillis();
        
        // 初始化 maxTimestamp 为一个不会导致溢出的最小值
        this.maxTimestamp = Long.MIN_VALUE + this.maxOutOfOrderness + 1;
        this.lastEventTime = System.currentTimeMillis();
        this.lastEmittedWatermark = Long.MIN_VALUE;
        this.isIdle = false;
        this.eventCount = 0L;
        this.watermarkCount = 0L;
        
        logger.info("IdleAwareWatermarkGenerator initialized: maxOutOfOrderness={}ms, idleTimeout={}ms", 
                   this.maxOutOfOrderness, this.idleTimeout);
    }
    
    /**
     * 每当一个新事件到达时调用
     * 
     * @param event 到达的事件
     * @param eventTimestamp 事件的时间戳
     * @param output 水印输出接口
     */
    @Override
    public void onEvent(T event, long eventTimestamp, WatermarkOutput output) {
        // 更新当前分区的最大时间戳
        if (eventTimestamp > this.maxTimestamp) {
            this.maxTimestamp = eventTimestamp;
        }
        
        // 记录当前处理时间，用于空闲检测
        this.lastEventTime = System.currentTimeMillis();
        
        // 增加事件计数
        this.eventCount++;
        
        // 如果之前处于空闲状态，现在恢复活跃
        if (this.isIdle) {
            this.isIdle = false;
            logger.debug("Partition resumed from idle state after {} events", this.eventCount);
        }
        
        // 可选：在事件到达时立即发射水印（适用于高频场景）
        // 这里我们选择在周期性调用中发射，以减少水印频率
        
        if (logger.isTraceEnabled() && this.eventCount % 1000 == 0) {
            logger.trace("Processed {} events, maxTimestamp={}, currentTime={}", 
                        this.eventCount, this.maxTimestamp, this.lastEventTime);
        }
    }
    
    /**
     * 周期性调用，用于生成并发出新的水印
     * 
     * @param output 水印输出接口
     */
    @Override
    public void onPeriodicEmit(WatermarkOutput output) {
        long currentTime = System.currentTimeMillis();
        long timeSinceLastEvent = currentTime - this.lastEventTime;
        
        // 计算基于最大乱序度的常规水印
        long potentialWatermark = this.maxTimestamp - this.maxOutOfOrderness;
        
        // 检查分区是否空闲
        boolean wasIdle = this.isIdle;
        this.isIdle = (timeSinceLastEvent > this.idleTimeout);
        
        long watermarkToEmit;
        
        if (this.isIdle) {
            // 如果空闲，将水印直接推进到当前分区的最大时间戳
            // 这相当于向系统声明："此分区暂时没有新数据，无需等待，请将时间推进到我已知的最新位置"
            watermarkToEmit = this.maxTimestamp;
            
            if (!wasIdle) {
                logger.debug("Partition became idle ({}ms since last event), advancing watermark to maxTimestamp: {}", 
                           timeSinceLastEvent, this.maxTimestamp);
            }
        } else {
            // 如果不空闲，正常发射常规水印
            watermarkToEmit = potentialWatermark;
        }
        
        // 确保水印单调递增
        if (watermarkToEmit > this.lastEmittedWatermark) {
            output.emitWatermark(new Watermark(watermarkToEmit));
            this.lastEmittedWatermark = watermarkToEmit;
            this.watermarkCount++;
            
            if (logger.isTraceEnabled()) {
                logger.trace("Emitted watermark: {} (idle={}, events={}, watermarks={})", 
                           watermarkToEmit, this.isIdle, this.eventCount, this.watermarkCount);
            }
        }
    }
    
    /**
     * 获取当前最大时间戳
     */
    public long getMaxTimestamp() {
        return maxTimestamp;
    }
    
    /**
     * 获取上次发射的水印值
     */
    public long getLastEmittedWatermark() {
        return lastEmittedWatermark;
    }
    
    /**
     * 判断当前是否处于空闲状态
     */
    public boolean isIdle() {
        return isIdle;
    }
    
    /**
     * 获取处理的事件总数
     */
    public long getEventCount() {
        return eventCount;
    }
    
    /**
     * 获取发射的水印总数
     */
    public long getWatermarkCount() {
        return watermarkCount;
    }
    
    /**
     * 获取距离上次事件的时间间隔
     */
    public long getTimeSinceLastEvent() {
        return System.currentTimeMillis() - this.lastEventTime;
    }
    
    /**
     * 重置统计信息（用于测试）
     */
    public void resetStats() {
        this.eventCount = 0L;
        this.watermarkCount = 0L;
        this.isIdle = false;
        this.lastEventTime = System.currentTimeMillis();
    }
    
    @Override
    public String toString() {
        return "IdleAwareWatermarkGenerator{" +
                "maxOutOfOrderness=" + maxOutOfOrderness +
                ", idleTimeout=" + idleTimeout +
                ", maxTimestamp=" + maxTimestamp +
                ", lastEmittedWatermark=" + lastEmittedWatermark +
                ", isIdle=" + isIdle +
                ", eventCount=" + eventCount +
                ", watermarkCount=" + watermarkCount +
                ", timeSinceLastEvent=" + getTimeSinceLastEvent() +
                '}';
    }
}
