package com.futures.job;

import com.futures.function.BaseOrderBookBuilder;
import com.futures.function.VirtualOrderBookBuilder;
import com.futures.model.*;
import com.futures.source.KafkaSourceFactory;
import com.futures.watermark.FuturesWatermarkStrategy;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.contrib.streaming.state.EmbeddedRocksDBStateBackend;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.datastream.BroadcastStream;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.util.OutputTag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;

/**
 * 期货全量订单簿Kafka作业主类
 * 
 * 基于事件时间及自定义空闲检测水印生成器的期货全量订单簿系统。
 * 
 * 核心特性：
 * 1. 事件时间处理机制，确保乱序和延迟数据的准确处理
 * 2. 自定义空闲检测水印生成器，防止分区空闲导致的时间停滞
 * 3. 分层架构：基础层处理单腿委托，虚拟层处理组合委托
 * 4. 广播状态模式：BBO信息在基础层和虚拟层之间传递
 * 5. 事件时间定时器：处理订单过期逻辑
 * 
 * <AUTHOR> System Team
 * @version 1.0
 */
public class FuturesOrderBookKafkaJob {
    
    private static final Logger logger = LoggerFactory.getLogger(FuturesOrderBookKafkaJob.class);
    
    // 作业配置参数
    private static final String PARAM_KAFKA_SERVERS = "kafka.bootstrap.servers";
    private static final String PARAM_CONSUMER_GROUP = "kafka.consumer.group";
    private static final String PARAM_MAX_OUT_OF_ORDERNESS = "watermark.max.out.of.orderness.ms";
    private static final String PARAM_IDLE_TIMEOUT = "watermark.idle.timeout.ms";
    private static final String PARAM_CHECKPOINT_INTERVAL = "checkpoint.interval.ms";
    private static final String PARAM_PARALLELISM = "parallelism";
    private static final String PARAM_STATE_BACKEND = "state.backend.type";
    
    // 默认配置值
    private static final String DEFAULT_KAFKA_SERVERS = "localhost:9092";
    private static final String DEFAULT_CONSUMER_GROUP = "futures-orderbook-consumer";
    private static final long DEFAULT_MAX_OUT_OF_ORDERNESS = 500L; // 500ms
    private static final long DEFAULT_IDLE_TIMEOUT = 10000L; // 10s
    private static final long DEFAULT_CHECKPOINT_INTERVAL = 30000L; // 30s
    private static final int DEFAULT_PARALLELISM = 1;
    private static final String DEFAULT_STATE_BACKEND = "memory"; // memory, rocksdb
    
    public static void main(String[] args) throws Exception {
        // 解析命令行参数
        ParameterTool params = ParameterTool.fromArgs(args);
        
        // 创建执行环境
        StreamExecutionEnvironment env = createExecutionEnvironment(params);
        
        // 配置作业参数
        configureJob(env, params);
        
        // 构建数据流处理管道
        buildDataPipeline(env, params);
        
        // 执行作业
        logger.warn("🚀 启动期货订单簿系统...");
        logger.warn("📋 配置信息: Kafka={}, 并行度={}, 水印策略={}ms/{}ms",
                   params.get(PARAM_KAFKA_SERVERS, DEFAULT_KAFKA_SERVERS),
                   params.getInt(PARAM_PARALLELISM, DEFAULT_PARALLELISM),
                   params.getLong(PARAM_MAX_OUT_OF_ORDERNESS, DEFAULT_MAX_OUT_OF_ORDERNESS),
                   params.getLong(PARAM_IDLE_TIMEOUT, DEFAULT_IDLE_TIMEOUT));
        env.execute("Futures Real-time Order Book System");
    }
    
    /**
     * 创建流执行环境
     */
    private static StreamExecutionEnvironment createExecutionEnvironment(ParameterTool params) {
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        
        // 设置并行度
        int parallelism = params.getInt(PARAM_PARALLELISM, DEFAULT_PARALLELISM);
        env.setParallelism(parallelism);
        
        logger.info("Created execution environment with parallelism: {}", parallelism);
        return env;
    }
    
    /**
     * 配置作业参数
     */
    private static void configureJob(StreamExecutionEnvironment env, ParameterTool params) {
        // 启用检查点
        long checkpointInterval = params.getLong(PARAM_CHECKPOINT_INTERVAL, DEFAULT_CHECKPOINT_INTERVAL);
        env.enableCheckpointing(checkpointInterval, CheckpointingMode.EXACTLY_ONCE);
        
        // 配置检查点参数
        env.getCheckpointConfig().setMinPauseBetweenCheckpoints(5000L);
        env.getCheckpointConfig().setCheckpointTimeout(60000L);
        env.getCheckpointConfig().setMaxConcurrentCheckpoints(1);
        env.getCheckpointConfig().setTolerableCheckpointFailureNumber(3);

        // 配置重启策略，防止频繁重启
        env.setRestartStrategy(org.apache.flink.api.common.restartstrategy.RestartStrategies.fixedDelayRestart(
            3, // 最多重启3次
            org.apache.flink.api.common.time.Time.of(10, java.util.concurrent.TimeUnit.SECONDS) // 重启间隔10秒
        ));
        
        // 配置状态后端
        configureStateBackend(env, params);
        
        // 设置水印生成间隔
        env.getConfig().setAutoWatermarkInterval(200L);
        
        // 设置全局作业参数
        env.getConfig().setGlobalJobParameters(params);
        
        logger.info("Configured job: checkpoint interval={}ms, watermark interval=200ms", checkpointInterval);
    }

    /**
     * 配置状态后端
     */
    private static void configureStateBackend(StreamExecutionEnvironment env, ParameterTool params) {
        String stateBackendType = params.get(PARAM_STATE_BACKEND, DEFAULT_STATE_BACKEND);

        try {
            switch (stateBackendType.toLowerCase()) {
                case "rocksdb":
                    env.setStateBackend(new EmbeddedRocksDBStateBackend(true));
                    logger.info("Configured RocksDB state backend");
                    break;
                case "memory":
                default:
                    // 使用默认的内存状态后端
                    logger.info("Using default memory state backend");
                    break;
            }
        } catch (Exception e) {
            logger.warn("Failed to configure {} state backend, falling back to memory: {}",
                       stateBackendType, e.getMessage());
            // 如果RocksDB配置失败，自动回退到内存状态后端
            logger.info("Using memory state backend as fallback");
        }
    }
    
    /**
     * 构建数据流处理管道
     */
    private static void buildDataPipeline(StreamExecutionEnvironment env, ParameterTool params) {
        // 获取配置参数
        String kafkaServers = params.get(PARAM_KAFKA_SERVERS, DEFAULT_KAFKA_SERVERS);
        String consumerGroup = params.get(PARAM_CONSUMER_GROUP, DEFAULT_CONSUMER_GROUP);
        long maxOutOfOrderness = params.getLong(PARAM_MAX_OUT_OF_ORDERNESS, DEFAULT_MAX_OUT_OF_ORDERNESS);
        long idleTimeout = params.getLong(PARAM_IDLE_TIMEOUT, DEFAULT_IDLE_TIMEOUT);
        
        logger.info("Pipeline configuration: kafka={}, group={}, maxOutOfOrderness={}ms, idleTimeout={}ms", 
                   kafkaServers, consumerGroup, maxOutOfOrderness, idleTimeout);
        
        // 1. 创建Kafka数据源
        KafkaSource<SingleLegOrder> singleLegSource = KafkaSourceFactory.createSingleLegOrderSourceFromEarliest(
            kafkaServers, consumerGroup + "-singleleg");
        
        KafkaSource<ComboOrder> comboSource = KafkaSourceFactory.createComboOrderSourceFromEarliest(
            kafkaServers, consumerGroup + "-combo");
        
        // 2. 创建水印策略
        WatermarkStrategy<SingleLegOrder> singleLegWatermarkStrategy = 
            FuturesWatermarkStrategy.forSingleLegOrders(
                Duration.ofMillis(maxOutOfOrderness), 
                Duration.ofMillis(idleTimeout));
        
        WatermarkStrategy<ComboOrder> comboWatermarkStrategy = 
            FuturesWatermarkStrategy.forComboOrders(
                Duration.ofMillis(maxOutOfOrderness), 
                Duration.ofMillis(idleTimeout));
        
        // 3. 创建数据流并应用水印策略
        DataStream<SingleLegOrder> singleLegStream = env
            .fromSource(singleLegSource, singleLegWatermarkStrategy, "SingleLeg-Kafka-Source")
            .name("SingleLeg Orders Stream")
            .uid("singleleg-source");
        
        DataStream<ComboOrder> comboStream = env
            .fromSource(comboSource, comboWatermarkStrategy, "Combo-Kafka-Source")
            .name("Combo Orders Stream")
            .uid("combo-source");
        
        // 4. 定义旁路输出和广播状态描述符
        final OutputTag<BBO_Update> bboOutputTag = new OutputTag<BBO_Update>("bbo-updates") {};
        final MapStateDescriptor<String, BBO_Update> bboStateDescriptor =
            new MapStateDescriptor<>(
                "bbo-broadcast-state",
                TypeInformation.of(String.class),
                TypeInformation.of(BBO_Update.class)
            );
        
        // 5. 处理基础层：单腿委托 -> 基础订单簿 + BBO
        SingleOutputStreamOperator<BaseOrderBook> baseBookStream = singleLegStream
            .keyBy(SingleLegOrder::getContractCde)
            .process(new BaseOrderBookBuilder(bboOutputTag))
            .name("Base OrderBook Builder")
            .uid("base-orderbook-builder");
        
        // 6. 获取BBO流并广播
        DataStream<BBO_Update> bboStream = baseBookStream.getSideOutput(bboOutputTag);
        BroadcastStream<BBO_Update> bboBroadcastStream = bboStream.broadcast(bboStateDescriptor);
        
        // 7. 处理虚拟层：组合委托 + BBO广播 -> 虚拟订单簿
        DataStream<VirtualOrderBook> virtualBookStream = comboStream
            .keyBy(ComboOrder::getComboId)
            .connect(bboBroadcastStream)
            .process(new VirtualOrderBookBuilder(bboStateDescriptor))
            .name("Virtual OrderBook Builder")
            .uid("virtual-orderbook-builder");
        
        // 8. 输出结果
        setupOutputSinks(baseBookStream, virtualBookStream, bboStream, params);
        
        logger.info("Data pipeline built successfully");
    }
    
    /**
     * 设置输出Sink
     */
    private static void setupOutputSinks(DataStream<BaseOrderBook> baseBookStream,
                                       DataStream<VirtualOrderBook> virtualBookStream,
                                       DataStream<BBO_Update> bboStream,
                                       ParameterTool params) {
        
        // 基础订单簿输出
        baseBookStream
            .print("BaseOrderBook")
            .name("Base OrderBook Sink")
            .uid("base-orderbook-sink");
        
        // 虚拟订单簿输出
        virtualBookStream
            .print("VirtualOrderBook")
            .name("Virtual OrderBook Sink")
            .uid("virtual-orderbook-sink");
        
        // BBO更新输出（可选）
        bboStream
            .print("BBO_Update")
            .name("BBO Update Sink")
            .uid("bbo-update-sink");
        
        // 在生产环境中，可以替换为Kafka、Redis、数据库等Sink
        // 例如：
        // baseBookStream.sinkTo(createKafkaSink("base-orderbook-topic"));
        // virtualBookStream.sinkTo(createRedisSink());
        
        logger.info("Output sinks configured");
    }
    
    /**
     * 打印作业配置信息
     */
    private static void printJobConfiguration(ParameterTool params) {
        logger.info("=== Futures Order Book Job Configuration ===");
        logger.info("Kafka Servers: {}", params.get(PARAM_KAFKA_SERVERS, DEFAULT_KAFKA_SERVERS));
        logger.info("Consumer Group: {}", params.get(PARAM_CONSUMER_GROUP, DEFAULT_CONSUMER_GROUP));
        logger.info("Max Out of Orderness: {}ms", params.getLong(PARAM_MAX_OUT_OF_ORDERNESS, DEFAULT_MAX_OUT_OF_ORDERNESS));
        logger.info("Idle Timeout: {}ms", params.getLong(PARAM_IDLE_TIMEOUT, DEFAULT_IDLE_TIMEOUT));
        logger.info("Checkpoint Interval: {}ms", params.getLong(PARAM_CHECKPOINT_INTERVAL, DEFAULT_CHECKPOINT_INTERVAL));
        logger.info("Parallelism: {}", params.getInt(PARAM_PARALLELISM, DEFAULT_PARALLELISM));
        logger.info("============================================");
    }
}
