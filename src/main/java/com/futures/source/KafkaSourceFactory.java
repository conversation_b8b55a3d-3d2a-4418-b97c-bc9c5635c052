package com.futures.source;

import com.futures.model.SingleLegOrder;
import com.futures.model.ComboOrder;
import com.futures.serialization.SingleLegOrderDeserializer;
import com.futures.serialization.ComboOrderDeserializer;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.connector.kafka.source.reader.deserializer.KafkaRecordDeserializationSchema;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Properties;

/**
 * Kafka数据源工厂
 * 
 * 提供创建各种Kafka数据源的工厂方法，包括：
 * 1. 单腿委托数据源
 * 2. 组合委托数据源
 * 3. 可配置的Kafka连接参数
 * 4. 统一的错误处理和监控
 * 
 * <AUTHOR> System Team
 * @version 1.0
 */
public class KafkaSourceFactory {
    
    private static final Logger logger = LoggerFactory.getLogger(KafkaSourceFactory.class);
    
    // 默认Kafka配置
    public static final String DEFAULT_BOOTSTRAP_SERVERS = "localhost:9092";
    public static final String DEFAULT_GROUP_ID = "futures-orderbook-consumer";
    
    // Topic名称常量
    public static final String SINGLE_LEG_TOPIC = "singleleg_order_data_event";
    public static final String COMBO_ORDER_TOPIC = "cmb_order_data_event";
    public static final String TRADE_DATA_TOPIC = "trade_data_event";
    
    /**
     * 创建单腿委托Kafka数据源
     * 
     * @param bootstrapServers Kafka服务器地址
     * @param groupId 消费者组ID
     * @return 单腿委托Kafka数据源
     */
    public static KafkaSource<SingleLegOrder> createSingleLegOrderSource(String bootstrapServers, String groupId) {
        logger.info("Creating SingleLegOrder Kafka source: servers={}, groupId={}, topic={}", 
                   bootstrapServers, groupId, SINGLE_LEG_TOPIC);
        
        return KafkaSource.<SingleLegOrder>builder()
                .setBootstrapServers(bootstrapServers)
                .setTopics(SINGLE_LEG_TOPIC)
                .setGroupId(groupId)
                .setStartingOffsets(OffsetsInitializer.latest())
                .setDeserializer(KafkaRecordDeserializationSchema.valueOnly(new SingleLegOrderDeserializer()))
                .setProperties(createKafkaConsumerProperties())
                .build();
    }
    
    /**
     * 创建单腿委托Kafka数据源（使用默认配置）
     */
    public static KafkaSource<SingleLegOrder> createSingleLegOrderSource() {
        return createSingleLegOrderSource(DEFAULT_BOOTSTRAP_SERVERS, DEFAULT_GROUP_ID + "-singleleg");
    }
    
    /**
     * 创建组合委托Kafka数据源
     * 
     * @param bootstrapServers Kafka服务器地址
     * @param groupId 消费者组ID
     * @return 组合委托Kafka数据源
     */
    public static KafkaSource<ComboOrder> createComboOrderSource(String bootstrapServers, String groupId) {
        logger.info("Creating ComboOrder Kafka source: servers={}, groupId={}, topic={}", 
                   bootstrapServers, groupId, COMBO_ORDER_TOPIC);
        
        return KafkaSource.<ComboOrder>builder()
                .setBootstrapServers(bootstrapServers)
                .setTopics(COMBO_ORDER_TOPIC)
                .setGroupId(groupId)
                .setStartingOffsets(OffsetsInitializer.latest())
                .setDeserializer(KafkaRecordDeserializationSchema.valueOnly(new ComboOrderDeserializer()))
                .setProperties(createKafkaConsumerProperties())
                .build();
    }
    
    /**
     * 创建组合委托Kafka数据源（使用默认配置）
     */
    public static KafkaSource<ComboOrder> createComboOrderSource() {
        return createComboOrderSource(DEFAULT_BOOTSTRAP_SERVERS, DEFAULT_GROUP_ID + "-combo");
    }
    
    /**
     * 创建从最早偏移量开始的单腿委托数据源（用于回放历史数据）
     */
    public static KafkaSource<SingleLegOrder> createSingleLegOrderSourceFromEarliest(String bootstrapServers, String groupId) {
        logger.info("Creating SingleLegOrder Kafka source from earliest: servers={}, groupId={}, topic={}", 
                   bootstrapServers, groupId, SINGLE_LEG_TOPIC);
        
        return KafkaSource.<SingleLegOrder>builder()
                .setBootstrapServers(bootstrapServers)
                .setTopics(SINGLE_LEG_TOPIC)
                .setGroupId(groupId)
                .setStartingOffsets(OffsetsInitializer.earliest())
                .setDeserializer(KafkaRecordDeserializationSchema.valueOnly(new SingleLegOrderDeserializer()))
                .setProperties(createKafkaConsumerProperties())
                .build();
    }
    
    /**
     * 创建从最早偏移量开始的组合委托数据源（用于回放历史数据）
     */
    public static KafkaSource<ComboOrder> createComboOrderSourceFromEarliest(String bootstrapServers, String groupId) {
        logger.info("Creating ComboOrder Kafka source from earliest: servers={}, groupId={}, topic={}", 
                   bootstrapServers, groupId, COMBO_ORDER_TOPIC);
        
        return KafkaSource.<ComboOrder>builder()
                .setBootstrapServers(bootstrapServers)
                .setTopics(COMBO_ORDER_TOPIC)
                .setGroupId(groupId)
                .setStartingOffsets(OffsetsInitializer.earliest())
                .setDeserializer(KafkaRecordDeserializationSchema.valueOnly(new ComboOrderDeserializer()))
                .setProperties(createKafkaConsumerProperties())
                .build();
    }
    
    /**
     * 创建Kafka消费者属性配置
     */
    private static Properties createKafkaConsumerProperties() {
        Properties props = new Properties();
        
        // 基础配置
        props.setProperty("enable.auto.commit", "true");
        props.setProperty("auto.commit.interval.ms", "1000");
        props.setProperty("session.timeout.ms", "30000");
        props.setProperty("heartbeat.interval.ms", "10000");
        
        // 性能优化配置
        props.setProperty("fetch.min.bytes", "1024");
        props.setProperty("fetch.max.wait.ms", "500");
        props.setProperty("max.partition.fetch.bytes", "1048576"); // 1MB
        props.setProperty("receive.buffer.bytes", "65536"); // 64KB
        props.setProperty("send.buffer.bytes", "131072"); // 128KB
        
        // 容错配置
        props.setProperty("retry.backoff.ms", "100");
        props.setProperty("reconnect.backoff.ms", "50");
        props.setProperty("reconnect.backoff.max.ms", "1000");
        
        // 安全配置（如果需要）
        // props.setProperty("security.protocol", "SASL_PLAINTEXT");
        // props.setProperty("sasl.mechanism", "PLAIN");
        
        return props;
    }
    
    /**
     * 创建高吞吐量配置的Kafka消费者属性
     */
    public static Properties createHighThroughputConsumerProperties() {
        Properties props = createKafkaConsumerProperties();
        
        // 高吞吐量优化
        props.setProperty("fetch.min.bytes", "16384"); // 16KB
        props.setProperty("fetch.max.wait.ms", "100");
        props.setProperty("max.partition.fetch.bytes", "2097152"); // 2MB
        props.setProperty("receive.buffer.bytes", "131072"); // 128KB
        props.setProperty("send.buffer.bytes", "262144"); // 256KB
        
        return props;
    }
    
    /**
     * 创建低延迟配置的Kafka消费者属性
     */
    public static Properties createLowLatencyConsumerProperties() {
        Properties props = createKafkaConsumerProperties();
        
        // 低延迟优化
        props.setProperty("fetch.min.bytes", "1");
        props.setProperty("fetch.max.wait.ms", "1");
        props.setProperty("max.partition.fetch.bytes", "524288"); // 512KB
        
        return props;
    }
    
    /**
     * 创建自定义配置的单腿委托数据源
     */
    public static KafkaSource<SingleLegOrder> createCustomSingleLegOrderSource(
            String bootstrapServers, String groupId, Properties customProps, OffsetsInitializer offsetsInitializer) {
        
        logger.info("Creating custom SingleLegOrder Kafka source: servers={}, groupId={}", 
                   bootstrapServers, groupId);
        
        return KafkaSource.<SingleLegOrder>builder()
                .setBootstrapServers(bootstrapServers)
                .setTopics(SINGLE_LEG_TOPIC)
                .setGroupId(groupId)
                .setStartingOffsets(offsetsInitializer)
                .setDeserializer(KafkaRecordDeserializationSchema.valueOnly(new SingleLegOrderDeserializer()))
                .setProperties(customProps)
                .build();
    }
    
    /**
     * 创建自定义配置的组合委托数据源
     */
    public static KafkaSource<ComboOrder> createCustomComboOrderSource(
            String bootstrapServers, String groupId, Properties customProps, OffsetsInitializer offsetsInitializer) {
        
        logger.info("Creating custom ComboOrder Kafka source: servers={}, groupId={}", 
                   bootstrapServers, groupId);
        
        return KafkaSource.<ComboOrder>builder()
                .setBootstrapServers(bootstrapServers)
                .setTopics(COMBO_ORDER_TOPIC)
                .setGroupId(groupId)
                .setStartingOffsets(offsetsInitializer)
                .setDeserializer(KafkaRecordDeserializationSchema.valueOnly(new ComboOrderDeserializer()))
                .setProperties(customProps)
                .build();
    }
}
