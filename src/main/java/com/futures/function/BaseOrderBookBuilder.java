package com.futures.function;

import com.futures.model.SingleLegOrder;
import com.futures.model.BaseOrderBook;
import com.futures.model.BBO_Update;
import org.apache.flink.api.common.state.MapState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.TreeMap;
import java.util.Map;

/**
 * 基础层订单簿构建器
 *
 * 处理单腿委托数据流，构建并维护基础层订单簿。主要功能包括：
 * 1. 维护每个合约的活跃委托状态
 * 2. 构建完整的订单簿（买卖价格深度）
 * 3. 提取并输出BBO信息到旁路输出
 * 4. 处理订单过期逻辑（基于事件时间定时器）
 * 5. 确保订单簿的准确性和最终一致性
 *
 * <AUTHOR> System Team
 * @version 1.0
 */
public class BaseOrderBookBuilder extends KeyedProcessFunction<String, SingleLegOrder, BaseOrderBook> {
    
    private static final Logger logger = LoggerFactory.getLogger(BaseOrderBookBuilder.class);
    
    /** 状态：存储当前合约的所有活跃委托 <委托号, 委托详情> */
    private transient MapState<String, SingleLegOrder> activeOrdersState;
    
    /** 旁路输出标签，用于发送BBO更新 */
    private final OutputTag<BBO_Update> bboOutputTag;
    
    /** 订单簿版本计数器状态 */
    private transient MapState<String, Long> versionState;
    
    /** 统计信息 */
    private long processedOrders = 0L;
    private long generatedBooks = 0L;
    private long expiredOrders = 0L;
    
    /**
     * 构造函数
     * 
     * @param bboOutputTag BBO旁路输出标签
     */
    public BaseOrderBookBuilder(OutputTag<BBO_Update> bboOutputTag) {
        this.bboOutputTag = bboOutputTag;
    }
    
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        
        // 初始化活跃订单状态
        MapStateDescriptor<String, SingleLegOrder> orderStateDescriptor =
            new MapStateDescriptor<>(
                "activeSingleLegOrders",
                TypeInformation.of(String.class),
                TypeInformation.of(SingleLegOrder.class)
            );
        activeOrdersState = getRuntimeContext().getMapState(orderStateDescriptor);
        
        // 初始化版本状态
        MapStateDescriptor<String, Long> versionStateDescriptor =
            new MapStateDescriptor<>(
                "orderBookVersion",
                TypeInformation.of(String.class),
                TypeInformation.of(Long.class)
            );
        versionState = getRuntimeContext().getMapState(versionStateDescriptor);
        
        logger.info("BaseOrderBookBuilder opened for task: {}", 
                   getRuntimeContext().getTaskNameWithSubtasks());
    }
    
    @Override
    public void processElement(SingleLegOrder order, Context ctx, Collector<BaseOrderBook> out) throws Exception {
        String contractCde = ctx.getCurrentKey();
        processedOrders++;
        
        // 检查订单状态变化，减少重复日志
        SingleLegOrder existingOrder = activeOrdersState.get(order.getOrdNbr());
        boolean isNewOrder = (existingOrder == null);
        boolean hasChanged = false;

        if (!isNewOrder) {
            // 检查是否有实际变化
            hasChanged = (existingOrder.getRmnVol() != order.getRmnVol()) ||
                         (Math.abs(existingOrder.getTrdPrc() - order.getTrdPrc()) > 0.001);
        }

        // 只在新增订单或有变化时输出日志
        if (isNewOrder || hasChanged) {
            if (order.isValid()) {
                if (isNewOrder) {
                    logger.info("➕ [{}] 新增订单: {} | {}单 | 价格: {} | 数量: {}",
                               contractCde, order.getOrdNbr(),
                               order.isBuyOrder() ? "买" : "卖",
                               order.getTrdPrc(), order.getRmnVol());
                } else {
                    logger.info("🔄 [{}] 更新订单: {} | {}单 | 价格: {} | 数量: {} (原: {})",
                               contractCde, order.getOrdNbr(),
                               order.isBuyOrder() ? "买" : "卖",
                               order.getTrdPrc(), order.getRmnVol(), existingOrder.getRmnVol());
                }
            } else {
                logger.info("❌ [{}] 取消订单: {} | {}单 | 价格: {} | 数量: {}",
                           contractCde, order.getOrdNbr(),
                           order.isBuyOrder() ? "买" : "卖",
                           order.getTrdPrc(), order.getRmnVol());
            }
        }
        
        // 验证订单数据
        if (!isValidOrder(order)) {
            logger.warn("Invalid order received: {}", order);
            return;
        }
        
        // 更新或移除委托状态
        if (order.isValid()) {
            // 添加或更新订单
            activeOrdersState.put(order.getOrdNbr(), order);
            
            // 为有过期时间的订单注册事件时间定时器
            if (order.getExpiryTimestamp() > 0) {
                ctx.timerService().registerEventTimeTimer(order.getExpiryTimestamp());
                if (logger.isTraceEnabled()) {
                    logger.trace("Registered expiry timer for order {} at timestamp {}", 
                               order.getOrdNbr(), order.getExpiryTimestamp());
                }
            }
        } else {
            // 移除无效订单（剩余数量为0或负数）
            SingleLegOrder removedOrder = activeOrdersState.get(order.getOrdNbr());
            if (removedOrder != null) {
                activeOrdersState.remove(order.getOrdNbr());
                logger.info("🗑️ [{}] 已移除订单: {} | 原价格: {} | 原数量: {}",
                           contractCde, order.getOrdNbr(), removedOrder.getTrdPrc(), removedOrder.getRmnVol());
            }
        }

        // 构建并输出完整订单簿
        BaseOrderBook orderBook = buildOrderBook(contractCde, ctx.timestamp());
        if (orderBook != null) {
            out.collect(orderBook);
            generatedBooks++;

            // 总是输出订单簿信息，无论是否为空
            if (!orderBook.isEmpty()) {
                logger.info("📈 [{}] 订单簿更新 - 最优买价: {}, 最优卖价: {}, 买量: {}, 卖量: {}, 价差: {}, 深度: {}买/{}卖",
                           contractCde, orderBook.getBestBid(), orderBook.getBestAsk(),
                           orderBook.getBestBidVolume(), orderBook.getBestAskVolume(), orderBook.getSpread(),
                           orderBook.getBids().size(), orderBook.getAsks().size());
            } else {
                logger.info("📉 [{}] 订单簿为空 - 总订单: {}, 有效订单: 0",
                           contractCde, getActiveOrderCount());
            }

            // 提取BBO并通过旁路输出发送
            BBO_Update bbo = extractBBO(orderBook, ctx.timestamp());
            if (bbo != null) {
                ctx.output(bboOutputTag, bbo);
                logger.info("📡 [{}] BBO输出 - 买价: {}, 卖价: {}",
                           contractCde, bbo.getBestBid(), bbo.getBestAsk());
            } else {
                logger.info("📡 [{}] BBO跳过 - 订单簿为空或无有效价格", contractCde);
            }
        }

        // 定期输出统计信息（降低频率）
        if (processedOrders % 500 == 0) {
            logger.info("📊 [{}] 统计 - 已处理: {} 订单, 生成: {} 订单簿, 过期: {} 订单, 活跃: {}",
                       contractCde, processedOrders, generatedBooks, expiredOrders, getActiveOrderCount());
        }
    }
    
    @Override
    public void onTimer(long timestamp, OnTimerContext ctx, Collector<BaseOrderBook> out) throws Exception {
        String contractCde = ctx.getCurrentKey();
        
        if (logger.isDebugEnabled()) {
            logger.debug("Timer fired at timestamp {} for contract {}", timestamp, contractCde);
        }
        
        // 查找并移除过期订单
        boolean hasExpiredOrders = false;
        
        // 遍历所有活跃订单，找到过期的订单
        for (Map.Entry<String, SingleLegOrder> entry : activeOrdersState.entries()) {
            SingleLegOrder order = entry.getValue();
            if (order.isExpired(timestamp)) {
                activeOrdersState.remove(entry.getKey());
                expiredOrders++;
                hasExpiredOrders = true;
                
                if (logger.isDebugEnabled()) {
                    logger.debug("Expired order {} at timestamp {} for contract {}", 
                               order.getOrdNbr(), timestamp, contractCde);
                }
            }
        }
        
        // 如果有订单过期，重新生成并输出订单簿
        if (hasExpiredOrders) {
            BaseOrderBook orderBook = buildOrderBook(contractCde, timestamp);
            if (orderBook != null) {
                out.collect(orderBook);
                generatedBooks++;
                
                // 提取BBO并通过旁路输出发送
                BBO_Update bbo = extractBBO(orderBook, timestamp);
                if (bbo != null) {
                    ctx.output(bboOutputTag, bbo);
                }
            }
        }
    }
    
    /**
     * 构建订单簿
     */
    private BaseOrderBook buildOrderBook(String contractCde, long timestamp) throws Exception {
        TreeMap<Double, Long> bids = new TreeMap<>(java.util.Collections.reverseOrder()); // 降序
        TreeMap<Double, Long> asks = new TreeMap<>(); // 升序

        int totalOrders = 0;
        int validOrders = 0;
        int buyOrders = 0;
        int sellOrders = 0;

        // 遍历所有活跃订单，按价格聚合数量
        for (SingleLegOrder order : activeOrdersState.values()) {
            totalOrders++;

            if (!order.isValid()) {
                continue; // 跳过无效订单
            }

            validOrders++;

            if (order.isBuyOrder()) {
                // 买单：聚合到bids
                bids.merge(order.getTrdPrc(), order.getRmnVol(), Long::sum);
                buyOrders++;
            } else if (order.isSellOrder()) {
                // 卖单：聚合到asks
                asks.merge(order.getTrdPrc(), order.getRmnVol(), Long::sum);
                sellOrders++;
            }
        }

        // 输出构建统计信息（仅在有变化时）
        if (validOrders > 0 || totalOrders != validOrders) {
            logger.info("🔧 [{}] 订单簿构建 - 总订单: {}, 有效: {}, 买单: {}, 卖单: {}",
                       contractCde, totalOrders, validOrders, buyOrders, sellOrders);
        }
        
        // 创建订单簿对象
        BaseOrderBook orderBook = new BaseOrderBook(contractCde, bids, asks);
        orderBook.setUpdateTimestamp(timestamp);
        
        // 设置版本号
        Long currentVersion = versionState.get(contractCde);
        if (currentVersion == null) {
            currentVersion = 0L;
        }
        orderBook.setVersion(++currentVersion);
        versionState.put(contractCde, currentVersion);
        
        return orderBook;
    }
    
    /**
     * 从订单簿提取BBO信息
     */
    private BBO_Update extractBBO(BaseOrderBook orderBook, long timestamp) {
        if (orderBook == null) {
            logger.warn("📡 BBO提取失败 - 订单簿为null");
            return null;
        }

        if (orderBook.isEmpty()) {
            logger.info("📡 BBO提取跳过 - 订单簿为空 (买价层级: {}, 卖价层级: {})",
                       orderBook.getBids().size(), orderBook.getAsks().size());
            return null;
        }

        // 检查是否有有效的买卖价
        double bestBid = orderBook.getBestBid();
        double bestAsk = orderBook.getBestAsk();

        if (bestBid <= 0 && bestAsk >= Double.MAX_VALUE) {
            logger.info("📡 BBO提取跳过 - 无有效价格 (买价: {}, 卖价: {})", bestBid, bestAsk);
            return null;
        }

        BBO_Update bbo = new BBO_Update();
        bbo.setInstrumentId(orderBook.getInstrumentId());
        bbo.setBestBid(bestBid);
        bbo.setBestAsk(bestAsk);
        bbo.setBestBidVolume(orderBook.getBestBidVolume());
        bbo.setBestAskVolume(orderBook.getBestAskVolume());
        bbo.setUpdateTimestamp(timestamp);

        return bbo;
    }
    
    /**
     * 验证订单数据的有效性
     */
    private boolean isValidOrder(SingleLegOrder order) {
        if (order == null) {
            return false;
        }
        
        if (order.getOrdNbr() == null || order.getOrdNbr().trim().isEmpty()) {
            logger.warn("Order missing order number: {}", order);
            return false;
        }
        
        if (order.getContractCde() == null || order.getContractCde().trim().isEmpty()) {
            logger.warn("Order missing contract code: {}", order);
            return false;
        }
        
        if (order.getBSTag() == null || 
            (!order.getBSTag().equalsIgnoreCase("B") && !order.getBSTag().equalsIgnoreCase("S"))) {
            logger.warn("Order has invalid BS tag: {}", order);
            return false;
        }
        
        if (order.getTrdPrc() <= 0) {
            logger.warn("Order has invalid price: {}", order);
            return false;
        }
        
        return true;
    }
    
    /**
     * 获取活跃订单数量
     */
    private int getActiveOrderCount() {
        try {
            int count = 0;
            for (SingleLegOrder order : activeOrdersState.values()) {
                count++;
            }
            return count;
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * 获取处理统计信息
     */
    public String getProcessingStats() {
        return String.format("Processed: %d orders, Generated: %d books, Expired: %d orders",
                           processedOrders, generatedBooks, expiredOrders);
    }
}
