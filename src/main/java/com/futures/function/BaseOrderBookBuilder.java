package com.futures.function;

import com.futures.model.SingleLegOrder;
import com.futures.model.BaseOrderBook;
import com.futures.model.BBO_Update;
import org.apache.flink.api.common.state.MapState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.TreeMap;
import java.util.Map;

/**
 * 基础层订单簿构建器
 * 
 * 处理单腿委托数据流，构建并维护基础层订单簿。主要功能包括：
 * 1. 维护每个合约的活跃委托状态
 * 2. 构建完整的订单簿（买卖价格深度）
 * 3. 提取并输出BBO信息到旁路输出
 * 4. 处理订单过期逻辑（基于事件时间定时器）
 * 5. 确保订单簿的准确性和最终一致性
 * 
 * <AUTHOR> System Team
 * @version 1.0
 */
public class BaseOrderBookBuilder extends ProcessFunction<SingleLegOrder, BaseOrderBook> {
    
    private static final Logger logger = LoggerFactory.getLogger(BaseOrderBookBuilder.class);
    
    /** 状态：存储当前合约的所有活跃委托 <委托号, 委托详情> */
    private transient MapState<String, SingleLegOrder> activeOrdersState;
    
    /** 旁路输出标签，用于发送BBO更新 */
    private final OutputTag<BBO_Update> bboOutputTag;
    
    /** 订单簿版本计数器状态 */
    private transient MapState<String, Long> versionState;
    
    /** 统计信息 */
    private long processedOrders = 0L;
    private long generatedBooks = 0L;
    private long expiredOrders = 0L;
    
    /**
     * 构造函数
     * 
     * @param bboOutputTag BBO旁路输出标签
     */
    public BaseOrderBookBuilder(OutputTag<BBO_Update> bboOutputTag) {
        this.bboOutputTag = bboOutputTag;
    }
    
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        
        // 初始化活跃订单状态
        MapStateDescriptor<String, SingleLegOrder> orderStateDescriptor =
            new MapStateDescriptor<>(
                "activeSingleLegOrders",
                TypeInformation.of(String.class),
                TypeInformation.of(SingleLegOrder.class)
            );
        activeOrdersState = getRuntimeContext().getMapState(orderStateDescriptor);
        
        // 初始化版本状态
        MapStateDescriptor<String, Long> versionStateDescriptor =
            new MapStateDescriptor<>(
                "orderBookVersion",
                TypeInformation.of(String.class),
                TypeInformation.of(Long.class)
            );
        versionState = getRuntimeContext().getMapState(versionStateDescriptor);
        
        logger.info("BaseOrderBookBuilder opened for task: {}", 
                   getRuntimeContext().getTaskNameWithSubtasks());
    }
    
    @Override
    public void processElement(SingleLegOrder order, Context ctx, Collector<BaseOrderBook> out) throws Exception {
        String contractCde = ctx
                .getCurrentKey();
        processedOrders++;
        
        if (logger.isDebugEnabled()) {
            logger.debug("Processing order for contract {}: {}", contractCde, order.getOrdNbr());
        }
        
        // 验证订单数据
        if (!isValidOrder(order)) {
            logger.warn("Invalid order received: {}", order);
            return;
        }
        
        // 更新或移除委托状态
        if (order.isValid()) {
            // 添加或更新订单
            activeOrdersState.put(order.getOrdNbr(), order);
            
            // 为有过期时间的订单注册事件时间定时器
            if (order.getExpiryTimestamp() > 0) {
                ctx.timerService().registerEventTimeTimer(order.getExpiryTimestamp());
                if (logger.isTraceEnabled()) {
                    logger.trace("Registered expiry timer for order {} at timestamp {}", 
                               order.getOrdNbr(), order.getExpiryTimestamp());
                }
            }
        } else {
            // 移除无效订单（剩余数量为0或负数）
            SingleLegOrder removedOrder = activeOrdersState.get(order.getOrdNbr());
            if (removedOrder != null) {
                activeOrdersState.remove(order.getOrdNbr());
                if (logger.isDebugEnabled()) {
                    logger.debug("Removed order {} from contract {}", order.getOrdNbr(), contractCde);
                }
            }
        }
        
        // 构建并输出完整订单簿
        BaseOrderBook orderBook = buildOrderBook(contractCde, ctx.timestamp());
        if (orderBook != null) {
            out.collect(orderBook);
            generatedBooks++;
            
            // 提取BBO并通过旁路输出发送
            BBO_Update bbo = extractBBO(orderBook, ctx.timestamp());
            if (bbo != null) {
                ctx.output(bboOutputTag, bbo);
            }
        }
        
        // 定期输出统计信息
        if (processedOrders % 1000 == 0) {
            logger.info("Processed {} orders, generated {} books, expired {} orders for contract {}", 
                       processedOrders, generatedBooks, expiredOrders, contractCde);
        }
    }
    
    @Override
    public void onTimer(long timestamp, OnTimerContext ctx, Collector<BaseOrderBook> out) throws Exception {
        String contractCde = ctx.getCurrentKey();
        
        if (logger.isDebugEnabled()) {
            logger.debug("Timer fired at timestamp {} for contract {}", timestamp, contractCde);
        }
        
        // 查找并移除过期订单
        boolean hasExpiredOrders = false;
        
        // 遍历所有活跃订单，找到过期的订单
        for (Map.Entry<String, SingleLegOrder> entry : activeOrdersState.entries()) {
            SingleLegOrder order = entry.getValue();
            if (order.isExpired(timestamp)) {
                activeOrdersState.remove(entry.getKey());
                expiredOrders++;
                hasExpiredOrders = true;
                
                if (logger.isDebugEnabled()) {
                    logger.debug("Expired order {} at timestamp {} for contract {}", 
                               order.getOrdNbr(), timestamp, contractCde);
                }
            }
        }
        
        // 如果有订单过期，重新生成并输出订单簿
        if (hasExpiredOrders) {
            BaseOrderBook orderBook = buildOrderBook(contractCde, timestamp);
            if (orderBook != null) {
                out.collect(orderBook);
                generatedBooks++;
                
                // 提取BBO并通过旁路输出发送
                BBO_Update bbo = extractBBO(orderBook, timestamp);
                if (bbo != null) {
                    ctx.output(bboOutputTag, bbo);
                }
            }
        }
    }
    
    /**
     * 构建订单簿
     */
    private BaseOrderBook buildOrderBook(String contractCde, long timestamp) throws Exception {
        TreeMap<Double, Long> bids = new TreeMap<>(java.util.Collections.reverseOrder()); // 降序
        TreeMap<Double, Long> asks = new TreeMap<>(); // 升序
        
        // 遍历所有活跃订单，按价格聚合数量
        for (SingleLegOrder order : activeOrdersState.values()) {
            if (!order.isValid()) {
                continue; // 跳过无效订单
            }
            
            if (order.isBuyOrder()) {
                // 买单：聚合到bids
                bids.merge(order.getTrdPrc(), order.getRmnVol(), Long::sum);
            } else if (order.isSellOrder()) {
                // 卖单：聚合到asks
                asks.merge(order.getTrdPrc(), order.getRmnVol(), Long::sum);
            }
        }
        
        // 创建订单簿对象
        BaseOrderBook orderBook = new BaseOrderBook(contractCde, bids, asks);
        orderBook.setUpdateTimestamp(timestamp);
        
        // 设置版本号
        Long currentVersion = versionState.get(contractCde);
        if (currentVersion == null) {
            currentVersion = 0L;
        }
        orderBook.setVersion(++currentVersion);
        versionState.put(contractCde, currentVersion);
        
        return orderBook;
    }
    
    /**
     * 从订单簿提取BBO信息
     */
    private BBO_Update extractBBO(BaseOrderBook orderBook, long timestamp) {
        if (orderBook == null || orderBook.isEmpty()) {
            return null;
        }
        
        BBO_Update bbo = new BBO_Update();
        bbo.setInstrumentId(orderBook.getInstrumentId());
        bbo.setBestBid(orderBook.getBestBid());
        bbo.setBestAsk(orderBook.getBestAsk());
        bbo.setBestBidVolume(orderBook.getBestBidVolume());
        bbo.setBestAskVolume(orderBook.getBestAskVolume());
        bbo.setUpdateTimestamp(timestamp);
        
        return bbo;
    }
    
    /**
     * 验证订单数据的有效性
     */
    private boolean isValidOrder(SingleLegOrder order) {
        if (order == null) {
            return false;
        }
        
        if (order.getOrdNbr() == null || order.getOrdNbr().trim().isEmpty()) {
            logger.warn("Order missing order number: {}", order);
            return false;
        }
        
        if (order.getContractCde() == null || order.getContractCde().trim().isEmpty()) {
            logger.warn("Order missing contract code: {}", order);
            return false;
        }
        
        if (order.getBSTag() == null || 
            (!order.getBSTag().equalsIgnoreCase("B") && !order.getBSTag().equalsIgnoreCase("S"))) {
            logger.warn("Order has invalid BS tag: {}", order);
            return false;
        }
        
        if (order.getTrdPrc() <= 0) {
            logger.warn("Order has invalid price: {}", order);
            return false;
        }
        
        return true;
    }
    
    /**
     * 获取处理统计信息
     */
    public String getProcessingStats() {
        return String.format("Processed: %d orders, Generated: %d books, Expired: %d orders", 
                           processedOrders, generatedBooks, expiredOrders);
    }
}
