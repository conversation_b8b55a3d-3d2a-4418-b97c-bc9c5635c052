package com.futures.function;

import com.futures.model.ComboOrder;
import com.futures.model.BBO_Update;
import com.futures.model.VirtualOrderBook;
import org.apache.flink.api.common.state.MapState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.state.ReadOnlyBroadcastState;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.co.KeyedBroadcastProcessFunction;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.TreeMap;
import java.util.Map;

/**
 * 虚拟层订单簿构建器
 * 
 * 处理组合委托数据流，结合BBO广播状态构建虚拟层订单簿。主要功能包括：
 * 1. 维护每个组合合约的活跃委托状态
 * 2. 接收并维护腿合约的BBO广播状态
 * 3. 基于组合委托和腿合约BBO构建虚拟订单簿
 * 4. 实现组合合约的理论定价逻辑
 * 5. 确保虚拟订单簿的准确性和实时性
 * 
 * <AUTHOR> System Team
 * @version 1.0
 */
public class VirtualOrderBookBuilder extends KeyedBroadcastProcessFunction<String, ComboOrder, BBO_Update, VirtualOrderBook> {
    
    private static final Logger logger = LoggerFactory.getLogger(VirtualOrderBookBuilder.class);
    
    /** Keyed State: 存储当前组合合约的活跃委托 */
    private transient MapState<String, ComboOrder> activeComboOrdersState;
    
    /** Broadcast State Descriptor: 用于访问BBO状态 */
    private final MapStateDescriptor<String, BBO_Update> bboStateDescriptor;
    
    /** 虚拟订单簿版本计数器状态 */
    private transient MapState<String, Long> versionState;
    
    /** 统计信息 */
    private long processedComboOrders = 0L;
    private long processedBBOUpdates = 0L;
    private long generatedVirtualBooks = 0L;
    private long skippedDueToMissingBBO = 0L;
    
    /**
     * 构造函数
     * 
     * @param bboStateDescriptor BBO广播状态描述符
     */
    public VirtualOrderBookBuilder(MapStateDescriptor<String, BBO_Update> bboStateDescriptor) {
        this.bboStateDescriptor = bboStateDescriptor;
    }
    
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        
        // 初始化活跃组合订单状态
        MapStateDescriptor<String, ComboOrder> comboOrderStateDescriptor =
            new MapStateDescriptor<>(
                "activeComboOrders",
                TypeInformation.of(String.class),
                TypeInformation.of(ComboOrder.class)
            );
        activeComboOrdersState = getRuntimeContext().getMapState(comboOrderStateDescriptor);
        
        // 初始化版本状态
        MapStateDescriptor<String, Long> versionStateDescriptor =
            new MapStateDescriptor<>(
                "virtualOrderBookVersion",
                TypeInformation.of(String.class),
                TypeInformation.of(Long.class)
            );
        versionState = getRuntimeContext().getMapState(versionStateDescriptor);
        
        logger.info("VirtualOrderBookBuilder opened for task: {}", 
                   getRuntimeContext().getTaskNameWithSubtasks());
    }
    
    @Override
    public void processBroadcastElement(BBO_Update bbo, Context ctx, Collector<VirtualOrderBook> out) throws Exception {
        processedBBOUpdates++;
        
        logger.info("📡 BBO更新 [{}]: 买价={}, 卖价={}, 买量={}, 卖量={}",
                   bbo.getInstrumentId(), bbo.getBestBid(), bbo.getBestAsk(),
                   bbo.getBestBidVolume(), bbo.getBestAskVolume());
        
        // 验证BBO数据
        if (!isValidBBO(bbo)) {
            logger.warn("Invalid BBO update received: {}", bbo);
            return;
        }
        
        // 将收到的BBO更新到广播状态中
        ctx.getBroadcastState(bboStateDescriptor).put(bbo.getInstrumentId(), bbo);
        
        if (logger.isTraceEnabled()) {
            logger.trace("Updated BBO state for instrument {}: bid={}, ask={}", 
                       bbo.getInstrumentId(), bbo.getBestBid(), bbo.getBestAsk());
        }
        
        // 定期输出统计信息
        if (processedBBOUpdates % 1000 == 0) {
            logger.info("Processed {} BBO updates", processedBBOUpdates);
        }
    }
    
    @Override
    public void processElement(ComboOrder comboOrder, ReadOnlyContext ctx, Collector<VirtualOrderBook> out) throws Exception {
        String comboId = ctx.getCurrentKey();
        processedComboOrders++;
        
        logger.info("🔗 [{}] 处理组合订单: {} | {}单 | 价格: {} | 数量: {} | 腿1: {} | 腿2: {}",
                   comboId, comboOrder.getOrdNbr(),
                   comboOrder.isBuyOrder() ? "买" : "卖",
                   comboOrder.getTrdPrc(), comboOrder.getRmnVol(),
                   comboOrder.getLeg1ContractCde(), comboOrder.getLeg2ContractCde());
        
        // 验证组合订单数据
        if (!isValidComboOrder(comboOrder)) {
            logger.warn("Invalid combo order received: {}", comboOrder);
            return;
        }
        
        // 解析组合信息（如果需要）
        if (comboOrder.getComboId() == null && comboOrder.getContractCde() != null) {
            comboOrder.parseComboFromContractCde();
        }
        
        // 更新 keyed state
        if (comboOrder.isValid()) {
            activeComboOrdersState.put(comboOrder.getOrdNbr(), comboOrder);
        } else {
            ComboOrder removedOrder = activeComboOrdersState.get(comboOrder.getOrdNbr());
            if (removedOrder != null) {
                activeComboOrdersState.remove(comboOrder.getOrdNbr());
                if (logger.isDebugEnabled()) {
                    logger.debug("Removed combo order {} from {}", comboOrder.getOrdNbr(), comboId);
                }
            }
        }
        
        // 从广播状态中获取依赖的腿的BBO
        ReadOnlyBroadcastState<String, BBO_Update> bboState = ctx.getBroadcastState(bboStateDescriptor);
        BBO_Update leg1BBO = bboState.get(comboOrder.getLeg1ContractCde());
        BBO_Update leg2BBO = bboState.get(comboOrder.getLeg2ContractCde());
        
        // 检查BBO数据完整性
        if (leg1BBO == null || leg2BBO == null) {
            skippedDueToMissingBBO++;
            if (logger.isDebugEnabled()) {
                logger.debug("BBO not ready for combo {}: leg1BBO={}, leg2BBO={}", 
                           comboId, leg1BBO != null, leg2BBO != null);
            }
            return;
        }
        
        if (!leg1BBO.isValid() || !leg2BBO.isValid()) {
            skippedDueToMissingBBO++;
            if (logger.isDebugEnabled()) {
                logger.debug("Invalid BBO data for combo {}: leg1Valid={}, leg2Valid={}", 
                           comboId, leg1BBO.isValid(), leg2BBO.isValid());
            }
            return;
        }
        
        // 构建虚拟订单簿
        VirtualOrderBook virtualBook = buildVirtualOrderBook(comboId, comboOrder, leg1BBO, leg2BBO, ctx.timestamp());
        if (virtualBook != null) {
            out.collect(virtualBook);
            generatedVirtualBooks++;
        }
        
        // 输出虚拟订单簿信息
        if (virtualBook != null && !virtualBook.isEmpty()) {
            logger.info("📊 [{}] 虚拟订单簿更新 - 最优买价: {}, 最优卖价: {}, 理论价差: {}, 买量: {}, 卖量: {}",
                       comboId, virtualBook.getBestBid(), virtualBook.getBestAsk(),
                       virtualBook.getTheoreticalSpread(), virtualBook.getBestBidVolume(), virtualBook.getBestAskVolume());
        }

        // 定期输出统计信息
        if (processedComboOrders % 50 == 0) {
            logger.info("📈 [{}] 组合统计 - 已处理: {} 订单, 生成: {} 虚拟订单簿, 跳过: {} (缺少BBO)",
                       comboId, processedComboOrders, generatedVirtualBooks, skippedDueToMissingBBO);
        }
    }
    
    /**
     * 构建虚拟订单簿
     */
    private VirtualOrderBook buildVirtualOrderBook(String comboId, ComboOrder currentOrder, 
                                                  BBO_Update leg1BBO, BBO_Update leg2BBO, 
                                                  long timestamp) throws Exception {
        
        TreeMap<Double, Long> bids = new TreeMap<>(java.util.Collections.reverseOrder()); // 降序
        TreeMap<Double, Long> asks = new TreeMap<>(); // 升序
        
        // 遍历所有活跃的组合订单，构建虚拟订单簿
        for (ComboOrder order : activeComboOrdersState.values()) {
            if (!order.isValid()) {
                continue;
            }
            
            // 计算组合订单的理论价格
            double theoreticalPrice = calculateTheoreticalPrice(order, leg1BBO, leg2BBO);
            if (Double.isNaN(theoreticalPrice) || Double.isInfinite(theoreticalPrice)) {
                continue; // 跳过无法定价的订单
            }
            
            // 根据买卖方向聚合到相应的价位
            if (order.isBuyOrder()) {
                bids.merge(theoreticalPrice, order.getRmnVol(), Long::sum);
            } else if (order.isSellOrder()) {
                asks.merge(theoreticalPrice, order.getRmnVol(), Long::sum);
            }
        }
        
        // 创建虚拟订单簿对象
        VirtualOrderBook virtualBook = new VirtualOrderBook(comboId, 
                                                           currentOrder.getLeg1ContractCde(), 
                                                           currentOrder.getLeg2ContractCde());
        virtualBook.setBids(bids);
        virtualBook.setAsks(asks);
        virtualBook.setUpdateTimestamp(timestamp);
        virtualBook.setLeg1BBO(leg1BBO);
        virtualBook.setLeg2BBO(leg2BBO);
        
        // 设置版本号
        Long currentVersion = versionState.get(comboId);
        if (currentVersion == null) {
            currentVersion = 0L;
        }
        virtualBook.setVersion(++currentVersion);
        versionState.put(comboId, currentVersion);
        
        return virtualBook;
    }
    
    /**
     * 计算组合订单的理论价格
     * 
     * 这里实现简单的价差定价逻辑：
     * - 对于买单：使用腿合约的卖价计算价差
     * - 对于卖单：使用腿合约的买价计算价差
     * 
     * 实际业务中可能需要更复杂的定价模型
     */
    private double calculateTheoreticalPrice(ComboOrder order, BBO_Update leg1BBO, BBO_Update leg2BBO) {
        if (order.isBuyOrder()) {
            // 买入组合：使用腿合约的卖价（ask）
            if (leg1BBO.hasAsk() && leg2BBO.hasAsk()) {
                return leg1BBO.getBestAsk() - leg2BBO.getBestAsk();
            }
        } else if (order.isSellOrder()) {
            // 卖出组合：使用腿合约的买价（bid）
            if (leg1BBO.hasBid() && leg2BBO.hasBid()) {
                return leg1BBO.getBestBid() - leg2BBO.getBestBid();
            }
        }
        
        // 如果无法定价，返回订单原始价格
        return order.getTrdPrc();
    }
    
    /**
     * 验证BBO数据的有效性
     */
    private boolean isValidBBO(BBO_Update bbo) {
        if (bbo == null) {
            return false;
        }
        
        if (bbo.getInstrumentId() == null || bbo.getInstrumentId().trim().isEmpty()) {
            logger.warn("BBO missing instrument ID: {}", bbo);
            return false;
        }
        
        return true;
    }
    
    /**
     * 验证组合订单数据的有效性
     */
    private boolean isValidComboOrder(ComboOrder order) {
        if (order == null) {
            return false;
        }
        
        if (order.getOrdNbr() == null || order.getOrdNbr().trim().isEmpty()) {
            logger.warn("Combo order missing order number: {}", order);
            return false;
        }
        
        if (order.getBSTag() == null || 
            (!order.getBSTag().equalsIgnoreCase("B") && !order.getBSTag().equalsIgnoreCase("S"))) {
            logger.warn("Combo order has invalid BS tag: {}", order);
            return false;
        }
        
        return true;
    }
    
    /**
     * 获取处理统计信息
     */
    public String getProcessingStats() {
        return String.format("Processed: %d combo orders, %d BBO updates, Generated: %d virtual books, Skipped: %d", 
                           processedComboOrders, processedBBOUpdates, generatedVirtualBooks, skippedDueToMissingBBO);
    }
}
