package com.futures.serialization;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.futures.model.ComboOrder;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * 组合委托JSON反序列化器
 * 
 * 将Kafka中的JSON消息反序列化为ComboOrder对象。
 * 支持容错处理、数据清洗和组合信息解析。
 * 
 * <AUTHOR> System Team
 * @version 1.0
 */
public class ComboOrderDeserializer implements DeserializationSchema<ComboOrder> {
    
    private static final Logger logger = LoggerFactory.getLogger(ComboOrderDeserializer.class);
    
    private transient ObjectMapper objectMapper;
    private long deserializedCount = 0L;
    private long errorCount = 0L;
    
    @Override
    public void open(InitializationContext context) throws Exception {
        // 初始化Jackson ObjectMapper
        objectMapper = new ObjectMapper();
        
        // 配置反序列化选项
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.configure(DeserializationFeature.FAIL_ON_NULL_FOR_PRIMITIVES, false);
        objectMapper.configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true);
        
        logger.info("ComboOrderDeserializer initialized");
    }
    
    @Override
    public ComboOrder deserialize(byte[] message) throws IOException {
        if (message == null || message.length == 0) {
            errorCount++;
            logger.warn("Received empty message");
            return null;
        }
        
        try {
            String jsonString = new String(message, StandardCharsets.UTF_8);
            
            if (logger.isTraceEnabled()) {
                logger.trace("Deserializing combo message: {}", jsonString);
            }
            
            ComboOrder order = objectMapper.readValue(jsonString, ComboOrder.class);
            
            // 数据清洗和验证
            if (order != null) {
                cleanAndValidateOrder(order);
                deserializedCount++;
                
                if (deserializedCount % 100 == 0) {
                    logger.debug("Deserialized {} combo orders, {} errors", deserializedCount, errorCount);
                }
            }
            
            return order;
            
        } catch (Exception e) {
            errorCount++;
            logger.error("Failed to deserialize combo order message: {}", 
                        new String(message, StandardCharsets.UTF_8), e);
            
            // 在生产环境中，可以选择返回null或抛出异常
            // 这里选择返回null，让Flink跳过这条消息
            return null;
        }
    }
    
    /**
     * 数据清洗和验证
     */
    private void cleanAndValidateOrder(ComboOrder order) {
        // 清理字符串字段的空白字符
        if (order.getContractCde() != null) {
            order.setContractCde(order.getContractCde().trim());
        }
        
        if (order.getOrdNbr() != null) {
            order.setOrdNbr(order.getOrdNbr().trim());
        }
        
        if (order.getBSTag() != null) {
            order.setBSTag(order.getBSTag().trim().toUpperCase());
        }
        
        if (order.getMembCde() != null) {
            order.setMembCde(order.getMembCde().trim());
        }
        
        if (order.getTrdCde() != null) {
            order.setTrdCde(order.getTrdCde().trim());
        }
        
        if (order.getSeatNbr() != null) {
            order.setSeatNbr(order.getSeatNbr().trim());
        }
        
        if (order.getTrdDt() != null) {
            order.setTrdDt(order.getTrdDt().trim());
        }
        
        // 解析组合信息
        parseComboInformation(order);
        
        // 验证和修正数值字段
        if (order.getRmnVol() < 0) {
            logger.warn("Negative remaining volume detected, setting to 0: {}", order.getOrdNbr());
            order.setRmnVol(0);
        }
        
        if (order.getTrdPrc() < 0) {
            logger.warn("Negative price detected: {}", order.getOrdNbr());
            order.setTrdPrc(0);
        }
        
        // 如果没有事件时间戳，使用当前时间
        if (order.getTimestamp() <= 0) {
            order.setTimestamp(System.currentTimeMillis());
        }
        
        // 验证买卖标志
        if (order.getBSTag() != null && 
            !order.getBSTag().equals("B") && !order.getBSTag().equals("S")) {
            logger.warn("Invalid BS tag '{}' for combo order {}, defaulting to 'B'", 
                       order.getBSTag(), order.getOrdNbr());
            order.setBSTag("B");
        }
    }
    
    /**
     * 解析组合信息
     * 从contract_cde字段解析出腿合约信息
     */
    private void parseComboInformation(ComboOrder order) {
        // 如果已经有组合ID，则跳过解析
        if (order.getComboId() != null && !order.getComboId().trim().isEmpty()) {
            return;
        }
        
        // 从contract_cde解析组合信息
        if (order.getContractCde() != null) {
            order.parseComboFromContractCde();
            
            if (logger.isTraceEnabled() && order.getComboId() != null) {
                logger.trace("Parsed combo info: {} -> leg1={}, leg2={}", 
                           order.getContractCde(), order.getLeg1ContractCde(), order.getLeg2ContractCde());
            }
        }
        
        // 如果仍然没有组合ID，生成一个默认的
        if (order.getComboId() == null || order.getComboId().trim().isEmpty()) {
            if (order.getLeg1ContractCde() != null && order.getLeg2ContractCde() != null) {
                order.setComboId(order.getLeg1ContractCde() + "-" + order.getLeg2ContractCde());
            } else if (order.getContractCde() != null) {
                // 使用原始合约代码作为组合ID
                order.setComboId(order.getContractCde());
                logger.debug("Using contract code as combo ID: {}", order.getContractCde());
            } else {
                // 最后兜底，使用订单号
                order.setComboId("COMBO-" + order.getOrdNbr());
                logger.warn("Generated fallback combo ID for order: {}", order.getOrdNbr());
            }
        }
        
        // 验证腿合约信息
        validateLegContracts(order);
    }
    
    /**
     * 验证腿合约信息
     */
    private void validateLegContracts(ComboOrder order) {
        if (order.getLeg1ContractCde() == null || order.getLeg1ContractCde().trim().isEmpty()) {
            logger.warn("Missing leg1 contract for combo order: {}", order.getOrdNbr());
        }
        
        if (order.getLeg2ContractCde() == null || order.getLeg2ContractCde().trim().isEmpty()) {
            logger.warn("Missing leg2 contract for combo order: {}", order.getOrdNbr());
        }
        
        // 检查腿合约是否相同（通常不应该相同）
        if (order.getLeg1ContractCde() != null && order.getLeg2ContractCde() != null &&
            order.getLeg1ContractCde().equals(order.getLeg2ContractCde())) {
            logger.warn("Leg1 and Leg2 contracts are the same for combo order: {} - {}", 
                       order.getOrdNbr(), order.getLeg1ContractCde());
        }
    }
    
    @Override
    public boolean isEndOfStream(ComboOrder nextElement) {
        // 对于无界流，永远不会结束
        return false;
    }
    
    @Override
    public TypeInformation<ComboOrder> getProducedType() {
        return TypeInformation.of(ComboOrder.class);
    }
    
    /**
     * 获取反序列化统计信息
     */
    public String getDeserializationStats() {
        return String.format("Deserialized: %d combo orders, Errors: %d", deserializedCount, errorCount);
    }
    
    /**
     * 重置统计计数器
     */
    public void resetStats() {
        deserializedCount = 0L;
        errorCount = 0L;
    }
}
