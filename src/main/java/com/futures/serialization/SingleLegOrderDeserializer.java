package com.futures.serialization;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.futures.model.SingleLegOrder;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * 单腿委托JSON反序列化器
 * 
 * 将Kafka中的JSON消息反序列化为SingleLegOrder对象。
 * 支持容错处理和数据清洗。
 * 
 * <AUTHOR> System Team
 * @version 1.0
 */
public class SingleLegOrderDeserializer implements DeserializationSchema<SingleLegOrder> {
    
    private static final Logger logger = LoggerFactory.getLogger(SingleLegOrderDeserializer.class);
    
    private transient ObjectMapper objectMapper;
    private long deserializedCount = 0L;
    private long errorCount = 0L;
    
    @Override
    public void open(InitializationContext context) throws Exception {
        // 初始化Jackson ObjectMapper
        objectMapper = new ObjectMapper();
        
        // 配置反序列化选项
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.configure(DeserializationFeature.FAIL_ON_NULL_FOR_PRIMITIVES, false);
        objectMapper.configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true);
        
        logger.info("SingleLegOrderDeserializer initialized");
    }
    
    @Override
    public SingleLegOrder deserialize(byte[] message) throws IOException {
        if (message == null || message.length == 0) {
            errorCount++;
            logger.warn("Received empty message");
            return null;
        }
        
        try {
            String jsonString = new String(message, StandardCharsets.UTF_8);
            
            if (logger.isTraceEnabled()) {
                logger.trace("Deserializing message: {}", jsonString);
            }
            
            SingleLegOrder order = objectMapper.readValue(jsonString, SingleLegOrder.class);
            
            // 数据清洗和验证
            if (order != null) {
                cleanAndValidateOrder(order);
                deserializedCount++;
                
                if (deserializedCount % 100 == 0) {
                    logger.info("📥 单腿委托反序列化: {} 成功, {} 错误", deserializedCount, errorCount);
                }
            }
            
            return order;
            
        } catch (Exception e) {
            errorCount++;
            logger.error("Failed to deserialize single leg order message: {}", 
                        new String(message, StandardCharsets.UTF_8), e);
            
            // 在生产环境中，可以选择返回null或抛出异常
            // 这里选择返回null，让Flink跳过这条消息
            return null;
        }
    }
    
    /**
     * 数据清洗和验证
     */
    private void cleanAndValidateOrder(SingleLegOrder order) {
        // 清理字符串字段的空白字符
        if (order.getContractCde() != null) {
            order.setContractCde(order.getContractCde().trim());
        }
        
        if (order.getOrdNbr() != null) {
            order.setOrdNbr(order.getOrdNbr().trim());
        }
        
        if (order.getBSTag() != null) {
            order.setBSTag(order.getBSTag().trim().toUpperCase());
        }
        
        if (order.getMembCde() != null) {
            order.setMembCde(order.getMembCde().trim());
        }
        
        if (order.getTrdCde() != null) {
            order.setTrdCde(order.getTrdCde().trim());
        }
        
        if (order.getSeatNbr() != null) {
            order.setSeatNbr(order.getSeatNbr().trim());
        }
        
        if (order.getTrdDt() != null) {
            order.setTrdDt(order.getTrdDt().trim());
        }
        
        // 验证和修正数值字段
        if (order.getRmnVol() < 0) {
            logger.warn("Negative remaining volume detected, setting to 0: {}", order.getOrdNbr());
            order.setRmnVol(0);
        }
        
        if (order.getTrdPrc() < 0) {
            logger.warn("Negative price detected: {}", order.getOrdNbr());
            order.setTrdPrc(0);
        }
        
        // 如果没有事件时间戳，使用当前时间
        if (order.getTimestamp() <= 0) {
            order.setTimestamp(System.currentTimeMillis());
        }
        
        // 验证买卖标志
        if (order.getBSTag() != null && 
            !order.getBSTag().equals("B") && !order.getBSTag().equals("S")) {
            logger.warn("Invalid BS tag '{}' for order {}, defaulting to 'B'", 
                       order.getBSTag(), order.getOrdNbr());
            order.setBSTag("B");
        }
    }
    
    @Override
    public boolean isEndOfStream(SingleLegOrder nextElement) {
        // 对于无界流，永远不会结束
        return false;
    }
    
    @Override
    public TypeInformation<SingleLegOrder> getProducedType() {
        return TypeInformation.of(SingleLegOrder.class);
    }
    
    /**
     * 获取反序列化统计信息
     */
    public String getDeserializationStats() {
        return String.format("Deserialized: %d orders, Errors: %d", deserializedCount, errorCount);
    }
    
    /**
     * 重置统计计数器
     */
    public void resetStats() {
        deserializedCount = 0L;
        errorCount = 0L;
    }
}
