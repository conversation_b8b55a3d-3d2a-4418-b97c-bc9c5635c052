package com.futures.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.util.Objects;

/**
 * 组合委托数据模型
 * 
 * 表示期货市场中的组合委托订单，通常涉及多个合约腿的价差交易。
 * 支持事件时间处理和虚拟订单簿构建。
 * 
 * <AUTHOR> System Team
 * @version 1.0
 */
public class ComboOrder implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** 组合合约标识 - 用作分区键 */
    @JsonProperty("combo_id")
    private String comboId;
    
    /** 第一腿合约代码 */
    @JsonProperty("leg1_contract_cde")
    private String leg1ContractCde;
    
    /** 第二腿合约代码 */
    @JsonProperty("leg2_contract_cde")
    private String leg2ContractCde;
    
    /** 委托编号 - 唯一标识 */
    @JsonProperty("ord_nbr")
    private String ordNbr;
    
    /** 买卖标志: B-买入, S-卖出 */
    @JsonProperty("b_s_tag")
    private String bSTag;

    /** 委托价格 (通常是价差) */
    @JsonProperty("ord_prc")
    private double trdPrc;

    /** 剩余数量 */
    @JsonProperty("ord_vol")
    private long rmnVol;
    
    /** 事件时间戳 (毫秒) */
    @JsonProperty("event_timestamp")
    private long timestamp;
    
    /** 会员代码 */
    @JsonProperty("memb_cde")
    private String membCde;
    
    /** 交易员代码 */
    @JsonProperty("trd_cde")
    private String trdCde;
    
    /** 席位号 */
    @JsonProperty("seat_nbr")
    private String seatNbr;
    
    /** 交易日期 */
    @JsonProperty("trd_dt")
    private String trdDt;
    
    /** 序列号 */
    @JsonProperty("_seq")
    private long seq;
    
    /** 事件编号 */
    @JsonProperty("_eno")
    private long eno;
    
    /** 合约代码 (原始字段，可能包含组合信息) */
    @JsonProperty("contract_cde")
    private String contractCde;
    
    // 默认构造函数
    public ComboOrder() {}
    
    // 全参构造函数
    public ComboOrder(String comboId, String leg1ContractCde, String leg2ContractCde,
                     String ordNbr, String bSTag, double trdPrc, long rmnVol, long timestamp) {
        this.comboId = comboId;
        this.leg1ContractCde = leg1ContractCde;
        this.leg2ContractCde = leg2ContractCde;
        this.ordNbr = ordNbr;
        this.bSTag = bSTag;
        this.trdPrc = trdPrc;
        this.rmnVol = rmnVol;
        this.timestamp = timestamp;
    }
    
    // Getter 和 Setter 方法
    public String getComboId() { return comboId; }
    public void setComboId(String comboId) { this.comboId = comboId; }
    
    public String getLeg1ContractCde() { return leg1ContractCde; }
    public void setLeg1ContractCde(String leg1ContractCde) { this.leg1ContractCde = leg1ContractCde; }
    
    public String getLeg2ContractCde() { return leg2ContractCde; }
    public void setLeg2ContractCde(String leg2ContractCde) { this.leg2ContractCde = leg2ContractCde; }
    
    public String getOrdNbr() { return ordNbr; }
    public void setOrdNbr(String ordNbr) { this.ordNbr = ordNbr; }
    
    public String getBSTag() { return bSTag; }
    public void setBSTag(String bSTag) { this.bSTag = bSTag; }
    
    public double getTrdPrc() { return trdPrc; }
    public void setTrdPrc(double trdPrc) { this.trdPrc = trdPrc; }
    
    public long getRmnVol() { return rmnVol; }
    public void setRmnVol(long rmnVol) { this.rmnVol = rmnVol; }
    
    public long getTimestamp() { return timestamp; }
    public void setTimestamp(long timestamp) { this.timestamp = timestamp; }
    
    public String getMembCde() { return membCde; }
    public void setMembCde(String membCde) { this.membCde = membCde; }
    
    public String getTrdCde() { return trdCde; }
    public void setTrdCde(String trdCde) { this.trdCde = trdCde; }
    
    public String getSeatNbr() { return seatNbr; }
    public void setSeatNbr(String seatNbr) { this.seatNbr = seatNbr; }
    
    public String getTrdDt() { return trdDt; }
    public void setTrdDt(String trdDt) { this.trdDt = trdDt; }
    
    public long getSeq() { return seq; }
    public void setSeq(long seq) { this.seq = seq; }
    
    public long getEno() { return eno; }
    public void setEno(long eno) { this.eno = eno; }
    
    public String getContractCde() { return contractCde; }
    public void setContractCde(String contractCde) { this.contractCde = contractCde; }
    
    /**
     * 判断订单是否为买单
     */
    public boolean isBuyOrder() {
        return "B".equalsIgnoreCase(bSTag);
    }
    
    /**
     * 判断订单是否为卖单
     */
    public boolean isSellOrder() {
        return "S".equalsIgnoreCase(bSTag);
    }
    
    /**
     * 判断订单是否有效（剩余数量大于0）
     */
    public boolean isValid() {
        return rmnVol > 0;
    }
    
    /**
     * 从合约代码解析组合信息
     * 例如: "SPD_EB2504_EB2505" -> leg1="EB2504", leg2="EB2505"
     */
    public void parseComboFromContractCde() {
        if (contractCde != null && contractCde.startsWith("SPD_")) {
            String[] parts = contractCde.split("_");
            if (parts.length >= 3) {
                this.leg1ContractCde = parts[1];
                this.leg2ContractCde = parts[2];
                this.comboId = leg1ContractCde + "-" + leg2ContractCde;
            }
        }
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ComboOrder that = (ComboOrder) o;
        return Objects.equals(ordNbr, that.ordNbr);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(ordNbr);
    }
    
    @Override
    public String toString() {
        return "ComboOrder{" +
                "comboId='" + comboId + '\'' +
                ", leg1ContractCde='" + leg1ContractCde + '\'' +
                ", leg2ContractCde='" + leg2ContractCde + '\'' +
                ", ordNbr='" + ordNbr + '\'' +
                ", bSTag='" + bSTag + '\'' +
                ", trdPrc=" + trdPrc +
                ", rmnVol=" + rmnVol +
                ", timestamp=" + timestamp +
                '}';
    }
}
