package com.futures.model;

import java.io.Serializable;
import java.util.TreeMap;
import java.util.Objects;
import java.util.Map;

/**
 * 虚拟层订单簿数据模型
 * 
 * 表示组合合约的虚拟订单簿，基于组合委托和相关腿合约的BBO信息构建。
 * 虚拟订单簿反映了组合合约的理论价格和深度信息。
 * 
 * <AUTHOR> System Team
 * @version 1.0
 */
public class VirtualOrderBook implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** 组合合约标识 */
    private String comboId;
    
    /** 第一腿合约代码 */
    private String leg1ContractCde;
    
    /** 第二腿合约代码 */
    private String leg2ContractCde;
    
    /** 买价订单簿 - 价格降序排列 (最高买价在前) */
    private TreeMap<Double, Long> bids;
    
    /** 卖价订单簿 - 价格升序排列 (最低卖价在前) */
    private TreeMap<Double, Long> asks;
    
    /** 订单簿更新时间戳 */
    private long updateTimestamp;
    
    /** 订单簿版本号 */
    private long version;
    
    /** 第一腿BBO信息 */
    private BBO_Update leg1BBO;
    
    /** 第二腿BBO信息 */
    private BBO_Update leg2BBO;
    
    // 默认构造函数
    public VirtualOrderBook() {
        this.bids = new TreeMap<>(java.util.Collections.reverseOrder()); // 降序
        this.asks = new TreeMap<>(); // 升序
        this.updateTimestamp = System.currentTimeMillis();
        this.version = 0L;
    }
    
    // 带参构造函数
    public VirtualOrderBook(String comboId) {
        this();
        this.comboId = comboId;
    }
    
    // 完整构造函数
    public VirtualOrderBook(String comboId, String leg1ContractCde, String leg2ContractCde) {
        this();
        this.comboId = comboId;
        this.leg1ContractCde = leg1ContractCde;
        this.leg2ContractCde = leg2ContractCde;
    }
    
    // Getter 和 Setter 方法
    public String getComboId() { return comboId; }
    public void setComboId(String comboId) { this.comboId = comboId; }
    
    public String getLeg1ContractCde() { return leg1ContractCde; }
    public void setLeg1ContractCde(String leg1ContractCde) { this.leg1ContractCde = leg1ContractCde; }
    
    public String getLeg2ContractCde() { return leg2ContractCde; }
    public void setLeg2ContractCde(String leg2ContractCde) { this.leg2ContractCde = leg2ContractCde; }
    
    public TreeMap<Double, Long> getBids() { return bids; }
    public void setBids(TreeMap<Double, Long> bids) { 
        this.bids = bids != null ? bids : new TreeMap<>(java.util.Collections.reverseOrder());
    }
    
    public TreeMap<Double, Long> getAsks() { return asks; }
    public void setAsks(TreeMap<Double, Long> asks) { 
        this.asks = asks != null ? asks : new TreeMap<>();
    }
    
    public long getUpdateTimestamp() { return updateTimestamp; }
    public void setUpdateTimestamp(long updateTimestamp) { this.updateTimestamp = updateTimestamp; }
    
    public long getVersion() { return version; }
    public void setVersion(long version) { this.version = version; }
    
    public BBO_Update getLeg1BBO() { return leg1BBO; }
    public void setLeg1BBO(BBO_Update leg1BBO) { this.leg1BBO = leg1BBO; }
    
    public BBO_Update getLeg2BBO() { return leg2BBO; }
    public void setLeg2BBO(BBO_Update leg2BBO) { this.leg2BBO = leg2BBO; }
    
    /**
     * 获取最优买价
     */
    public double getBestBid() {
        return bids.isEmpty() ? 0.0 : bids.firstKey();
    }
    
    /**
     * 获取最优卖价
     */
    public double getBestAsk() {
        return asks.isEmpty() ? Double.MAX_VALUE : asks.firstKey();
    }
    
    /**
     * 获取最优买价数量
     */
    public long getBestBidVolume() {
        return bids.isEmpty() ? 0L : bids.firstEntry().getValue();
    }
    
    /**
     * 获取最优卖价数量
     */
    public long getBestAskVolume() {
        return asks.isEmpty() ? 0L : asks.firstEntry().getValue();
    }
    
    /**
     * 获取买卖价差
     */
    public double getSpread() {
        double bestBid = getBestBid();
        double bestAsk = getBestAsk();
        if (bestBid <= 0 || bestAsk >= Double.MAX_VALUE) {
            return Double.MAX_VALUE;
        }
        return bestAsk - bestBid;
    }
    
    /**
     * 获取中间价
     */
    public double getMidPrice() {
        double bestBid = getBestBid();
        double bestAsk = getBestAsk();
        if (bestBid <= 0 || bestAsk >= Double.MAX_VALUE) {
            return 0.0;
        }
        return (bestBid + bestAsk) / 2.0;
    }
    
    /**
     * 获取理论价差 (基于腿合约BBO)
     */
    public double getTheoreticalSpread() {
        if (leg1BBO == null || leg2BBO == null) {
            return Double.NaN;
        }
        // 简单的价差计算: leg1_mid - leg2_mid
        return leg1BBO.getMidPrice() - leg2BBO.getMidPrice();
    }
    
    /**
     * 判断BBO数据是否完整
     */
    public boolean hasBBOData() {
        return leg1BBO != null && leg2BBO != null && 
               leg1BBO.isValid() && leg2BBO.isValid();
    }
    
    /**
     * 获取总买量
     */
    public long getTotalBidVolume() {
        return bids.values().stream().mapToLong(Long::longValue).sum();
    }
    
    /**
     * 获取总卖量
     */
    public long getTotalAskVolume() {
        return asks.values().stream().mapToLong(Long::longValue).sum();
    }
    
    /**
     * 判断订单簿是否为空
     */
    public boolean isEmpty() {
        return bids.isEmpty() && asks.isEmpty();
    }
    
    /**
     * 判断订单簿是否有效
     */
    public boolean isValid() {
        return !isEmpty() && getBestBid() <= getBestAsk();
    }
    
    /**
     * 获取指定深度的买价
     */
    public Map<Double, Long> getBidsAtDepth(int depth) {
        TreeMap<Double, Long> result = new TreeMap<>(java.util.Collections.reverseOrder());
        int count = 0;
        for (Map.Entry<Double, Long> entry : bids.entrySet()) {
            if (count >= depth) break;
            result.put(entry.getKey(), entry.getValue());
            count++;
        }
        return result;
    }
    
    /**
     * 获取指定深度的卖价
     */
    public Map<Double, Long> getAsksAtDepth(int depth) {
        TreeMap<Double, Long> result = new TreeMap<>();
        int count = 0;
        for (Map.Entry<Double, Long> entry : asks.entrySet()) {
            if (count >= depth) break;
            result.put(entry.getKey(), entry.getValue());
            count++;
        }
        return result;
    }
    
    /**
     * 增加版本号
     */
    public void incrementVersion() {
        this.version++;
        this.updateTimestamp = System.currentTimeMillis();
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        VirtualOrderBook that = (VirtualOrderBook) o;
        return Objects.equals(comboId, that.comboId) &&
               Objects.equals(bids, that.bids) &&
               Objects.equals(asks, that.asks);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(comboId, bids, asks);
    }
    
    @Override
    public String toString() {
        return "VirtualOrderBook{" +
                "comboId='" + comboId + '\'' +
                ", leg1='" + leg1ContractCde + '\'' +
                ", leg2='" + leg2ContractCde + '\'' +
                ", bestBid=" + getBestBid() +
                ", bestAsk=" + getBestAsk() +
                ", spread=" + getSpread() +
                ", theoreticalSpread=" + getTheoreticalSpread() +
                ", bidLevels=" + bids.size() +
                ", askLevels=" + asks.size() +
                ", version=" + version +
                ", updateTimestamp=" + updateTimestamp +
                '}';
    }
}
