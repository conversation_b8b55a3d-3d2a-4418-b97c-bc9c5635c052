package com.futures.model;

import java.io.Serializable;
import java.util.Objects;

/**
 * 最优买卖价更新数据模型
 * 
 * 表示某个合约的最优买价和最优卖价信息，用于广播状态传递。
 * 由基础层订单簿构建器生成，供虚拟层订单簿构建器使用。
 * 
 * <AUTHOR> System Team
 * @version 1.0
 */
public class BBO_Update implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** 合约标识 */
    private String instrumentId;
    
    /** 最优买价 */
    private double bestBid;
    
    /** 最优卖价 */
    private double bestAsk;
    
    /** 最优买价数量 */
    private long bestBidVolume;
    
    /** 最优卖价数量 */
    private long bestAskVolume;
    
    /** 更新时间戳 */
    private long updateTimestamp;
    
    // 默认构造函数
    public BBO_Update() {}
    
    // 全参构造函数
    public BBO_Update(String instrumentId, double bestBid, double bestAsk) {
        this.instrumentId = instrumentId;
        this.bestBid = bestBid;
        this.bestAsk = bestAsk;
        this.updateTimestamp = System.currentTimeMillis();
    }
    
    // 完整构造函数
    public BBO_Update(String instrumentId, double bestBid, double bestAsk, 
                     long bestBidVolume, long bestAskVolume, long updateTimestamp) {
        this.instrumentId = instrumentId;
        this.bestBid = bestBid;
        this.bestAsk = bestAsk;
        this.bestBidVolume = bestBidVolume;
        this.bestAskVolume = bestAskVolume;
        this.updateTimestamp = updateTimestamp;
    }
    
    // Getter 和 Setter 方法
    public String getInstrumentId() { return instrumentId; }
    public void setInstrumentId(String instrumentId) { this.instrumentId = instrumentId; }
    
    public double getBestBid() { return bestBid; }
    public void setBestBid(double bestBid) { this.bestBid = bestBid; }
    
    public double getBestAsk() { return bestAsk; }
    public void setBestAsk(double bestAsk) { this.bestAsk = bestAsk; }
    
    public long getBestBidVolume() { return bestBidVolume; }
    public void setBestBidVolume(long bestBidVolume) { this.bestBidVolume = bestBidVolume; }
    
    public long getBestAskVolume() { return bestAskVolume; }
    public void setBestAskVolume(long bestAskVolume) { this.bestAskVolume = bestAskVolume; }
    
    public long getUpdateTimestamp() { return updateTimestamp; }
    public void setUpdateTimestamp(long updateTimestamp) { this.updateTimestamp = updateTimestamp; }
    
    /**
     * 获取买卖价差
     */
    public double getSpread() {
        if (bestBid <= 0 || bestAsk <= 0) {
            return Double.MAX_VALUE;
        }
        return bestAsk - bestBid;
    }
    
    /**
     * 获取中间价
     */
    public double getMidPrice() {
        if (bestBid <= 0 || bestAsk <= 0) {
            return 0.0;
        }
        return (bestBid + bestAsk) / 2.0;
    }
    
    /**
     * 判断BBO是否有效
     */
    public boolean isValid() {
        return bestBid > 0 && bestAsk > 0 && bestBid <= bestAsk;
    }
    
    /**
     * 判断是否有买价
     */
    public boolean hasBid() {
        return bestBid > 0;
    }
    
    /**
     * 判断是否有卖价
     */
    public boolean hasAsk() {
        return bestAsk > 0 && bestAsk < Double.MAX_VALUE;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        BBO_Update that = (BBO_Update) o;
        return Double.compare(that.bestBid, bestBid) == 0 &&
               Double.compare(that.bestAsk, bestAsk) == 0 &&
               bestBidVolume == that.bestBidVolume &&
               bestAskVolume == that.bestAskVolume &&
               Objects.equals(instrumentId, that.instrumentId);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(instrumentId, bestBid, bestAsk, bestBidVolume, bestAskVolume);
    }
    
    @Override
    public String toString() {
        return "BBO_Update{" +
                "instrumentId='" + instrumentId + '\'' +
                ", bestBid=" + bestBid +
                ", bestAsk=" + bestAsk +
                ", bestBidVolume=" + bestBidVolume +
                ", bestAskVolume=" + bestAskVolume +
                ", spread=" + getSpread() +
                ", updateTimestamp=" + updateTimestamp +
                '}';
    }
}
