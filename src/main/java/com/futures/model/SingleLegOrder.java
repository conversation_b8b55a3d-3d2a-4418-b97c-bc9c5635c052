package com.futures.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.util.Objects;

/**
 * 单腿委托数据模型
 * 
 * 表示期货市场中的单腿委托订单，包含订单的基本信息、价格、数量等关键字段。
 * 支持事件时间处理和订单过期机制。
 * 
 * <AUTHOR> System Team
 * @version 1.0
 */
public class SingleLegOrder implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** 合约代码 - 用作分区键 */
    @JsonProperty("contract_cde")
    private String contractCde;
    
    /** 委托编号 - 唯一标识 */
    @JsonProperty("ord_nbr")
    private String ordNbr;
    
    /** 买卖标志: B-买入, S-卖出 */
    @JsonProperty("bs_tag")
    private String bSTag;
    
    /** 委托价格 */
    @JsonProperty("trd_prc")
    private double trdPrc;
    
    /** 剩余数量 */
    @JsonProperty("rmn_vol")
    private long rmnVol;
    
    /** 事件时间戳 (毫秒) */
    @JsonProperty("event_timestamp")
    private long timestamp;
    
    /** 订单过期时间戳 (毫秒) - 可选字段 */
    @JsonProperty("expiry_timestamp")
    private long expiryTimestamp;
    
    /** 会员代码 */
    @JsonProperty("memb_cde")
    private String membCde;
    
    /** 交易员代码 */
    @JsonProperty("trd_cde")
    private String trdCde;
    
    /** 席位号 */
    @JsonProperty("seat_nbr")
    private String seatNbr;
    
    /** 交易日期 */
    @JsonProperty("trd_dt")
    private String trdDt;
    
    /** 序列号 */
    @JsonProperty("_seq")
    private long seq;
    
    /** 事件编号 */
    @JsonProperty("_eno")
    private long eno;
    
    // 默认构造函数
    public SingleLegOrder() {}
    
    // 全参构造函数
    public SingleLegOrder(String contractCde, String ordNbr, String bSTag, 
                         double trdPrc, long rmnVol, long timestamp) {
        this.contractCde = contractCde;
        this.ordNbr = ordNbr;
        this.bSTag = bSTag;
        this.trdPrc = trdPrc;
        this.rmnVol = rmnVol;
        this.timestamp = timestamp;
    }
    
    // Getter 和 Setter 方法
    public String getContractCde() { return contractCde; }
    public void setContractCde(String contractCde) { this.contractCde = contractCde; }
    
    public String getOrdNbr() { return ordNbr; }
    public void setOrdNbr(String ordNbr) { this.ordNbr = ordNbr; }
    
    public String getBSTag() { return bSTag; }
    public void setBSTag(String bSTag) { this.bSTag = bSTag; }
    
    public double getTrdPrc() { return trdPrc; }
    public void setTrdPrc(double trdPrc) { this.trdPrc = trdPrc; }
    
    public long getRmnVol() { return rmnVol; }
    public void setRmnVol(long rmnVol) { this.rmnVol = rmnVol; }
    
    public long getTimestamp() { return timestamp; }
    public void setTimestamp(long timestamp) { this.timestamp = timestamp; }
    
    public long getExpiryTimestamp() { return expiryTimestamp; }
    public void setExpiryTimestamp(long expiryTimestamp) { this.expiryTimestamp = expiryTimestamp; }
    
    public String getMembCde() { return membCde; }
    public void setMembCde(String membCde) { this.membCde = membCde; }
    
    public String getTrdCde() { return trdCde; }
    public void setTrdCde(String trdCde) { this.trdCde = trdCde; }
    
    public String getSeatNbr() { return seatNbr; }
    public void setSeatNbr(String seatNbr) { this.seatNbr = seatNbr; }
    
    public String getTrdDt() { return trdDt; }
    public void setTrdDt(String trdDt) { this.trdDt = trdDt; }
    
    public long getSeq() { return seq; }
    public void setSeq(long seq) { this.seq = seq; }
    
    public long getEno() { return eno; }
    public void setEno(long eno) { this.eno = eno; }
    
    /**
     * 判断订单是否为买单
     */
    public boolean isBuyOrder() {
        return "B".equalsIgnoreCase(bSTag);
    }
    
    /**
     * 判断订单是否为卖单
     */
    public boolean isSellOrder() {
        return "S".equalsIgnoreCase(bSTag);
    }
    
    /**
     * 判断订单是否有效（剩余数量大于0）
     */
    public boolean isValid() {
        return rmnVol > 0;
    }
    
    /**
     * 判断订单是否已过期
     */
    public boolean isExpired(long currentTimestamp) {
        return expiryTimestamp > 0 && currentTimestamp >= expiryTimestamp;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SingleLegOrder that = (SingleLegOrder) o;
        return Objects.equals(ordNbr, that.ordNbr);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(ordNbr);
    }
    
    @Override
    public String toString() {
        return "SingleLegOrder{" +
                "contractCde='" + contractCde + '\'' +
                ", ordNbr='" + ordNbr + '\'' +
                ", bSTag='" + bSTag + '\'' +
                ", trdPrc=" + trdPrc +
                ", rmnVol=" + rmnVol +
                ", timestamp=" + timestamp +
                ", expiryTimestamp=" + expiryTimestamp +
                '}';
    }
}
