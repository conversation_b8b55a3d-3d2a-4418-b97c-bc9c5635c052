package com.futures.model;

import java.io.Serializable;
import java.util.TreeMap;
import java.util.Objects;
import java.util.Map;

/**
 * 基础层订单簿数据模型
 * 
 * 表示单个合约的完整订单簿，包含所有价位的买卖委托信息。
 * 使用TreeMap保证价格的有序性，买价按降序排列，卖价按升序排列。
 * 
 * <AUTHOR> System Team
 * @version 1.0
 */
public class BaseOrderBook implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** 合约标识 */
    private String instrumentId;
    
    /** 买价订单簿 - 价格降序排列 (最高买价在前) */
    private TreeMap<Double, Long> bids;
    
    /** 卖价订单簿 - 价格升序排列 (最低卖价在前) */
    private TreeMap<Double, Long> asks;
    
    /** 订单簿更新时间戳 */
    private long updateTimestamp;
    
    /** 订单簿版本号 */
    private long version;
    
    // 默认构造函数
    public BaseOrderBook() {
        this.bids = new TreeMap<>(java.util.Collections.reverseOrder()); // 降序
        this.asks = new TreeMap<>(); // 升序
        this.updateTimestamp = System.currentTimeMillis();
        this.version = 0L;
    }
    
    // 带参构造函数
    public BaseOrderBook(String instrumentId) {
        this();
        this.instrumentId = instrumentId;
    }
    
    // 完整构造函数
    public BaseOrderBook(String instrumentId, TreeMap<Double, Long> bids, TreeMap<Double, Long> asks) {
        this.instrumentId = instrumentId;
        this.bids = bids != null ? bids : new TreeMap<>(java.util.Collections.reverseOrder());
        this.asks = asks != null ? asks : new TreeMap<>();
        this.updateTimestamp = System.currentTimeMillis();
        this.version = 0L;
    }
    
    // Getter 和 Setter 方法
    public String getInstrumentId() { return instrumentId; }
    public void setInstrumentId(String instrumentId) { this.instrumentId = instrumentId; }
    
    public TreeMap<Double, Long> getBids() { return bids; }
    public void setBids(TreeMap<Double, Long> bids) { 
        this.bids = bids != null ? bids : new TreeMap<>(java.util.Collections.reverseOrder());
    }
    
    public TreeMap<Double, Long> getAsks() { return asks; }
    public void setAsks(TreeMap<Double, Long> asks) { 
        this.asks = asks != null ? asks : new TreeMap<>();
    }
    
    public long getUpdateTimestamp() { return updateTimestamp; }
    public void setUpdateTimestamp(long updateTimestamp) { this.updateTimestamp = updateTimestamp; }
    
    public long getVersion() { return version; }
    public void setVersion(long version) { this.version = version; }
    
    /**
     * 获取最优买价
     */
    public double getBestBid() {
        return bids.isEmpty() ? 0.0 : bids.firstKey();
    }
    
    /**
     * 获取最优卖价
     */
    public double getBestAsk() {
        return asks.isEmpty() ? Double.MAX_VALUE : asks.firstKey();
    }
    
    /**
     * 获取最优买价数量
     */
    public long getBestBidVolume() {
        return bids.isEmpty() ? 0L : bids.firstEntry().getValue();
    }
    
    /**
     * 获取最优卖价数量
     */
    public long getBestAskVolume() {
        return asks.isEmpty() ? 0L : asks.firstEntry().getValue();
    }
    
    /**
     * 获取买卖价差
     */
    public double getSpread() {
        double bestBid = getBestBid();
        double bestAsk = getBestAsk();
        if (bestBid <= 0 || bestAsk >= Double.MAX_VALUE) {
            return Double.MAX_VALUE;
        }
        return bestAsk - bestBid;
    }
    
    /**
     * 获取中间价
     */
    public double getMidPrice() {
        double bestBid = getBestBid();
        double bestAsk = getBestAsk();
        if (bestBid <= 0 || bestAsk >= Double.MAX_VALUE) {
            return 0.0;
        }
        return (bestBid + bestAsk) / 2.0;
    }
    
    /**
     * 获取总买量
     */
    public long getTotalBidVolume() {
        return bids.values().stream().mapToLong(Long::longValue).sum();
    }
    
    /**
     * 获取总卖量
     */
    public long getTotalAskVolume() {
        return asks.values().stream().mapToLong(Long::longValue).sum();
    }
    
    /**
     * 判断订单簿是否为空
     */
    public boolean isEmpty() {
        return bids.isEmpty() && asks.isEmpty();
    }
    
    /**
     * 判断订单簿是否有效
     */
    public boolean isValid() {
        return !isEmpty() && getBestBid() <= getBestAsk();
    }
    
    /**
     * 获取指定深度的买价
     */
    public Map<Double, Long> getBidsAtDepth(int depth) {
        TreeMap<Double, Long> result = new TreeMap<>(java.util.Collections.reverseOrder());
        int count = 0;
        for (Map.Entry<Double, Long> entry : bids.entrySet()) {
            if (count >= depth) break;
            result.put(entry.getKey(), entry.getValue());
            count++;
        }
        return result;
    }
    
    /**
     * 获取指定深度的卖价
     */
    public Map<Double, Long> getAsksAtDepth(int depth) {
        TreeMap<Double, Long> result = new TreeMap<>();
        int count = 0;
        for (Map.Entry<Double, Long> entry : asks.entrySet()) {
            if (count >= depth) break;
            result.put(entry.getKey(), entry.getValue());
            count++;
        }
        return result;
    }
    
    /**
     * 增加版本号
     */
    public void incrementVersion() {
        this.version++;
        this.updateTimestamp = System.currentTimeMillis();
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        BaseOrderBook that = (BaseOrderBook) o;
        return Objects.equals(instrumentId, that.instrumentId) &&
               Objects.equals(bids, that.bids) &&
               Objects.equals(asks, that.asks);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(instrumentId, bids, asks);
    }
    
    @Override
    public String toString() {
        return "BaseOrderBook{" +
                "instrumentId='" + instrumentId + '\'' +
                ", bestBid=" + getBestBid() +
                ", bestAsk=" + getBestAsk() +
                ", spread=" + getSpread() +
                ", bidLevels=" + bids.size() +
                ", askLevels=" + asks.size() +
                ", version=" + version +
                ", updateTimestamp=" + updateTimestamp +
                '}';
    }
}
