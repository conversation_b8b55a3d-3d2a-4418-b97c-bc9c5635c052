<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN">
    <Properties>
        <!-- 日志文件路径 -->
        <Property name="LOG_HOME">./logs</Property>
        <!-- 日志文件名前缀 -->
        <Property name="LOG_PREFIX">futures-orderbook</Property>
        <!-- 日志格式 -->
        <Property name="LOG_PATTERN">%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n</Property>
        <!-- 控制台日志格式（带颜色） -->
        <Property name="CONSOLE_PATTERN">%d{HH:mm:ss.SSS} [%t] %highlight{%-5level} %style{%logger{36}}{cyan} - %msg%n</Property>
    </Properties>

    <Appenders>
        <!-- 控制台输出 -->
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="${CONSOLE_PATTERN}"/>
            <ThresholdFilter level="INFO" onMatch="ACCEPT" onMismatch="DENY"/>
        </Console>

        <!-- 应用日志文件 -->
        <RollingFile name="AppFile" fileName="${LOG_HOME}/${LOG_PREFIX}.log"
                     filePattern="${LOG_HOME}/${LOG_PREFIX}-%d{yyyy-MM-dd}-%i.log.gz">
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="100MB"/>
            </Policies>
            <DefaultRolloverStrategy max="30"/>
            <ThresholdFilter level="INFO" onMatch="ACCEPT" onMismatch="DENY"/>
        </RollingFile>

        <!-- 错误日志文件 -->
        <RollingFile name="ErrorFile" fileName="${LOG_HOME}/${LOG_PREFIX}-error.log"
                     filePattern="${LOG_HOME}/${LOG_PREFIX}-error-%d{yyyy-MM-dd}-%i.log.gz">
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="50MB"/>
            </Policies>
            <DefaultRolloverStrategy max="30"/>
            <ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
        </RollingFile>

        <!-- 调试日志文件（仅在开发环境使用） -->
        <RollingFile name="DebugFile" fileName="${LOG_HOME}/${LOG_PREFIX}-debug.log"
                     filePattern="${LOG_HOME}/${LOG_PREFIX}-debug-%d{yyyy-MM-dd}-%i.log.gz">
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="200MB"/>
            </Policies>
            <DefaultRolloverStrategy max="7"/>
            <ThresholdFilter level="DEBUG" onMatch="ACCEPT" onMismatch="DENY"/>
        </RollingFile>

        <!-- 性能监控日志 -->
        <RollingFile name="MetricsFile" fileName="${LOG_HOME}/${LOG_PREFIX}-metrics.log"
                     filePattern="${LOG_HOME}/${LOG_PREFIX}-metrics-%d{yyyy-MM-dd}-%i.log.gz">
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="50MB"/>
            </Policies>
            <DefaultRolloverStrategy max="15"/>
        </RollingFile>
    </Appenders>

    <Loggers>
        <!-- 应用程序日志 -->
        <Logger name="com.futures" level="INFO" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="AppFile"/>
            <AppenderRef ref="ErrorFile"/>
        </Logger>

        <!-- Flink框架日志 -->
        <Logger name="org.apache.flink" level="INFO" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="AppFile"/>
        </Logger>

        <!-- Kafka客户端日志 -->
        <Logger name="org.apache.kafka" level="WARN" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="AppFile"/>
        </Logger>

        <!-- Jackson序列化日志 -->
        <Logger name="com.fasterxml.jackson" level="WARN" additivity="false">
            <AppenderRef ref="AppFile"/>
        </Logger>

        <!-- 水印生成器详细日志（开发调试用） -->
        <Logger name="com.futures.watermark" level="DEBUG" additivity="false">
            <AppenderRef ref="DebugFile"/>
            <AppenderRef ref="ErrorFile"/>
        </Logger>

        <!-- 订单簿构建器详细日志（开发调试用） -->
        <Logger name="com.futures.function" level="DEBUG" additivity="false">
            <AppenderRef ref="DebugFile"/>
            <AppenderRef ref="ErrorFile"/>
        </Logger>

        <!-- 性能监控日志 -->
        <Logger name="metrics" level="INFO" additivity="false">
            <AppenderRef ref="MetricsFile"/>
        </Logger>

        <!-- 根日志配置 -->
        <Root level="INFO">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="AppFile"/>
            <AppenderRef ref="ErrorFile"/>
        </Root>
    </Loggers>
</Configuration>
