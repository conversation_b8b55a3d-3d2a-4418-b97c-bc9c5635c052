# ?????????????
# Futures Order Book System Configuration

# ==================== Kafka?? ====================
# Kafka?????
kafka.bootstrap.servers=localhost:9092
# ????Kafka??????????
# kafka.bootstrap.servers=************:8087

# Kafka????ID
kafka.consumer.group=futures-orderbook-consumer

# Kafka????
kafka.topic.singleleg=singleleg_order_data_event
kafka.topic.combo=cmb_order_data_event
kafka.topic.trade=trade_data_event

# ==================== ???? ====================
# ???????????
watermark.max.out.of.orderness.ms=500

# ??????????
watermark.idle.timeout.ms=10000

# ??????????
watermark.interval.ms=200

# ==================== Flink???? ====================
# ?????
parallelism=4

# ?????????
checkpoint.interval.ms=30000

# ???????????
checkpoint.timeout.ms=60000

# ????????????????
checkpoint.min.pause.ms=5000

# ?????????
checkpoint.max.concurrent=1

# ???????????
checkpoint.tolerable.failures=3

# ==================== ?????? ====================
# ???????memory, fs, rocksdb
state.backend.type=rocksdb

# RocksDB??????
state.backend.rocksdb.incremental=true
state.backend.rocksdb.memory.managed=true

# ==================== ?????? ====================
# ???????
network.buffer.memory.fraction=0.1
network.buffer.memory.min=64mb
network.buffer.memory.max=1gb

# ?????????
taskmanager.memory.process.size=2gb
taskmanager.memory.flink.size=1.5gb

# ==================== ???? ====================
# ?????????
metrics.reporter.interval=30

# JMX?????
metrics.reporter.jmx.class=org.apache.flink.metrics.jmx.JMXReporter
metrics.reporter.jmx.port=9999

# ==================== ????? ====================
# ???????
orderbook.max.depth=20

# ????????????
orderbook.expiry.check.interval.ms=5000

# BBO??????????
bbo.update.throttle.ms=100

# ==================== ???? ====================
# ?????none, fixed-delay, failure-rate, exponential-delay
restart.strategy=failure-rate

# ?????????
restart.strategy.failure-rate.max-failures-per-interval=3
restart.strategy.failure-rate.failure-rate-interval=5min
restart.strategy.failure-rate.delay=30s

# ==================== ?????? ====================
# ????????
debug.mode=false

# ??????????
debug.print.stats=true

# ????????????
debug.stats.interval.ms=10000

# ??????????
debug.late.data.output=true

# ==================== ?????? ====================
# ?????dev, test, prod
environment=dev

# ????Web UI
web.ui.enabled=true
web.ui.port=8081

# ?????????
historyserver.enabled=false

# ==================== ???? ====================
# ????SSL
security.ssl.enabled=false

# Kerberos??????????
# security.kerberos.login.keytab=
# security.kerberos.login.principal=

# ==================== ???? ====================
# ????????
object.reuse=true

# ??????
latency.tracking.enabled=false
latency.tracking.interval=2000

# ??????????
generic.types.enabled=true

# ==================== ??????? ====================
# ????????
combo.pricing.model=simple_spread

# ??????????
combo.theoretical.pricing.enabled=true

# ??????????
price.precision=4

# ??????????
volume.precision=0

# ????????
order.validation.enabled=true

# ????????
data.cleaning.enabled=true
