# Flink配置文件
# 用于本地开发和测试环境

# ==============================================================================
# 通用配置
# ==============================================================================

# JobManager的主机名和端口
jobmanager.rpc.address: localhost
jobmanager.rpc.port: 6123

# JobManager的绑定主机名
jobmanager.bind-host: 0.0.0.0

# TaskManager的主机名
taskmanager.host: localhost

# TaskManager的绑定主机名
taskmanager.bind-host: 0.0.0.0

# TaskManager的数据端口
taskmanager.data.port: 6121

# TaskManager的RPC端口
taskmanager.rpc.port: 6122

# 每个TaskManager的slot数量
taskmanager.numberOfTaskSlots: 4

# 并行度
parallelism.default: 4

# ==============================================================================
# 内存配置
# ==============================================================================

# JobManager内存配置
jobmanager.memory.process.size: 1600m
jobmanager.memory.flink.size: 1280m
jobmanager.memory.heap.size: 1024m
jobmanager.memory.off-heap.size: 256m

# TaskManager内存配置
taskmanager.memory.process.size: 2048m
taskmanager.memory.flink.size: 1728m
taskmanager.memory.framework.heap.size: 128m
taskmanager.memory.task.heap.size: 1024m
taskmanager.memory.managed.size: 512m
taskmanager.memory.network.min: 64m
taskmanager.memory.network.max: 256m

# ==============================================================================
# 检查点和状态后端配置
# ==============================================================================

# 状态后端
state.backend: rocksdb

# 检查点存储目录
state.checkpoints.dir: file:///tmp/flink-checkpoints

# 保存点存储目录
state.savepoints.dir: file:///tmp/flink-savepoints

# RocksDB状态后端配置
state.backend.rocksdb.localdir: /tmp/flink-rocksdb
state.backend.rocksdb.memory.managed: true
state.backend.rocksdb.memory.fixed-per-slot: 256m

# 增量检查点
state.backend.incremental: true

# ==============================================================================
# 网络配置
# ==============================================================================

# 网络缓冲区配置
taskmanager.memory.network.fraction: 0.1
taskmanager.memory.network.min: 64mb
taskmanager.memory.network.max: 1gb

# 网络缓冲区超时
taskmanager.network.memory.buffers-per-channel: 2
taskmanager.network.memory.floating-buffers-per-gate: 8

# ==============================================================================
# Web UI配置
# ==============================================================================

# Web UI端口
rest.port: 8081

# Web UI绑定地址
rest.bind-address: 0.0.0.0

# 是否启用Web UI
web.submit.enable: true
web.cancel.enable: true

# ==============================================================================
# 指标配置
# ==============================================================================

# 指标报告器
metrics.reporters: jmx

# JMX指标报告器配置
metrics.reporter.jmx.class: org.apache.flink.metrics.jmx.JMXReporter
metrics.reporter.jmx.port: 9999

# 指标范围
metrics.scope.jm: <host>.jobmanager
metrics.scope.jm.job: <host>.jobmanager.<job_name>
metrics.scope.tm: <host>.taskmanager.<tm_id>
metrics.scope.tm.job: <host>.taskmanager.<tm_id>.<job_name>
metrics.scope.task: <host>.taskmanager.<tm_id>.<job_name>.<task_name>.<subtask_index>
metrics.scope.operator: <host>.taskmanager.<tm_id>.<job_name>.<operator_name>.<subtask_index>

# ==============================================================================
# 高可用配置（单机模式不需要）
# ==============================================================================

# 高可用模式：none, zookeeper, kubernetes
high-availability: none

# ==============================================================================
# 安全配置
# ==============================================================================

# SSL配置
security.ssl.enabled: false

# ==============================================================================
# 高级配置
# ==============================================================================

# 类加载器解析顺序
classloader.resolve-order: child-first

# 是否检查泄漏的类加载器
classloader.check-leaked-classloader: true

# 任务取消超时时间
task.cancellation.timeout: 30000

# 任务取消间隔
task.cancellation.interval: 30000

# ==============================================================================
# 开发调试配置
# ==============================================================================

# 是否启用对象重用
pipeline.object-reuse: true

# 是否启用通用类型
pipeline.generic-types: true

# 延迟跟踪
metrics.latency.interval: 10000
metrics.latency.granularity: operator

# ==============================================================================
# 容错配置
# ==============================================================================

# 重启策略
restart-strategy: failure-rate
restart-strategy.failure-rate.max-failures-per-interval: 3
restart-strategy.failure-rate.failure-rate-interval: 5 min
restart-strategy.failure-rate.delay: 10 s

# ==============================================================================
# 编译器配置
# ==============================================================================

# 代码生成
table.exec.codegen.enabled: true
table.exec.codegen.length-max: 4000

# ==============================================================================
# 环境变量
# ==============================================================================

# JVM参数
env.java.opts: -Xms512m -Xmx2048m -XX:+UseG1GC -XX:MaxGCPauseMillis=200

# 日志配置
env.log.dir: ./logs
env.log.max: 10
