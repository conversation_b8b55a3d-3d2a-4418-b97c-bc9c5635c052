package com.futures.function;

import com.futures.model.SingleLegOrder;
import com.futures.model.BaseOrderBook;
import com.futures.model.BBO_Update;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.streaming.util.ProcessFunctionTestHarnesses;
import org.apache.flink.streaming.util.OneInputStreamOperatorTestHarness;
import org.apache.flink.util.OutputTag;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * BaseOrderBookBuilder 单元测试
 * 
 * 测试基础层订单簿构建器的核心功能：
 * 1. 订单簿构建逻辑
 * 2. BBO提取和旁路输出
 * 3. 订单状态管理
 * 4. 事件时间定时器处理
 * 
 * <AUTHOR> System Team
 */
class BaseOrderBookBuilderTest {
    
    private OneInputStreamOperatorTestHarness<SingleLegOrder, BaseOrderBook> testHarness;
    private BaseOrderBookBuilder orderBookBuilder;
    private OutputTag<BBO_Update> bboOutputTag;
    
    @BeforeEach
    void setUp() throws Exception {
        bboOutputTag = new OutputTag<BBO_Update>("bbo-updates") {};
        orderBookBuilder = new BaseOrderBookBuilder(bboOutputTag);
        
        testHarness = ProcessFunctionTestHarnesses.forKeyedProcessFunction(
            orderBookBuilder,
            SingleLegOrder::getContractCde,
            String.class
        );
        
        testHarness.open();
    }
    
    @Test
    @DisplayName("测试单个买单订单簿构建")
    void testSingleBuyOrderBookBuilding() throws Exception {
        String contractCde = "TEST_CONTRACT";
        SingleLegOrder buyOrder = createTestOrder("ORDER1", contractCde, "B", 100.0, 1000L, 1000L);
        
        testHarness.processElement(buyOrder, 1000L);
        
        // 验证订单簿输出
        List<BaseOrderBook> orderBooks = testHarness.extractOutputValues();
        assertEquals(1, orderBooks.size());
        
        BaseOrderBook orderBook = orderBooks.get(0);
        assertEquals(contractCde, orderBook.getInstrumentId());
        assertEquals(100.0, orderBook.getBestBid());
        assertEquals(1000L, orderBook.getBestBidVolume());
        assertEquals(Double.MAX_VALUE, orderBook.getBestAsk()); // 没有卖单
        
        // 验证BBO旁路输出
        List<BBO_Update> bboUpdates = testHarness.getSideOutput(bboOutputTag);
        assertEquals(1, bboUpdates.size());
        
        BBO_Update bbo = bboUpdates.get(0);
        assertEquals(contractCde, bbo.getInstrumentId());
        assertEquals(100.0, bbo.getBestBid());
        assertEquals(1000L, bbo.getBestBidVolume());
    }
    
    @Test
    @DisplayName("测试单个卖单订单簿构建")
    void testSingleSellOrderBookBuilding() throws Exception {
        String contractCde = "TEST_CONTRACT";
        SingleLegOrder sellOrder = createTestOrder("ORDER1", contractCde, "S", 105.0, 500L, 1000L);
        
        testHarness.processElement(sellOrder, 1000L);
        
        List<BaseOrderBook> orderBooks = testHarness.extractOutputValues();
        assertEquals(1, orderBooks.size());
        
        BaseOrderBook orderBook = orderBooks.get(0);
        assertEquals(contractCde, orderBook.getInstrumentId());
        assertEquals(0.0, orderBook.getBestBid()); // 没有买单
        assertEquals(105.0, orderBook.getBestAsk());
        assertEquals(500L, orderBook.getBestAskVolume());
    }
    
    @Test
    @DisplayName("测试买卖双向订单簿构建")
    void testBidAskOrderBookBuilding() throws Exception {
        String contractCde = "TEST_CONTRACT";
        SingleLegOrder buyOrder = createTestOrder("ORDER1", contractCde, "B", 100.0, 1000L, 1000L);
        SingleLegOrder sellOrder = createTestOrder("ORDER2", contractCde, "S", 105.0, 500L, 1500L);
        
        testHarness.processElement(buyOrder, 1000L);
        testHarness.processElement(sellOrder, 1500L);
        
        List<BaseOrderBook> orderBooks = testHarness.extractOutputValues();
        assertEquals(2, orderBooks.size());
        
        // 检查最终订单簿状态
        BaseOrderBook finalOrderBook = orderBooks.get(1);
        assertEquals(contractCde, finalOrderBook.getInstrumentId());
        assertEquals(100.0, finalOrderBook.getBestBid());
        assertEquals(1000L, finalOrderBook.getBestBidVolume());
        assertEquals(105.0, finalOrderBook.getBestAsk());
        assertEquals(500L, finalOrderBook.getBestAskVolume());
        assertEquals(5.0, finalOrderBook.getSpread());
        assertEquals(102.5, finalOrderBook.getMidPrice());
    }
    
    @Test
    @DisplayName("测试相同价格订单聚合")
    void testSamePriceOrderAggregation() throws Exception {
        String contractCde = "TEST_CONTRACT";
        SingleLegOrder buyOrder1 = createTestOrder("ORDER1", contractCde, "B", 100.0, 1000L, 1000L);
        SingleLegOrder buyOrder2 = createTestOrder("ORDER2", contractCde, "B", 100.0, 500L, 1500L);
        
        testHarness.processElement(buyOrder1, 1000L);
        testHarness.processElement(buyOrder2, 1500L);
        
        List<BaseOrderBook> orderBooks = testHarness.extractOutputValues();
        BaseOrderBook finalOrderBook = orderBooks.get(1);
        
        // 验证相同价格的订单被聚合
        assertEquals(100.0, finalOrderBook.getBestBid());
        assertEquals(1500L, finalOrderBook.getBestBidVolume()); // 1000 + 500
        assertEquals(1, finalOrderBook.getBids().size()); // 只有一个价位
    }
    
    @Test
    @DisplayName("测试多价位订单簿深度")
    void testMultiLevelOrderBookDepth() throws Exception {
        String contractCde = "TEST_CONTRACT";
        
        // 添加多个不同价位的买单
        testHarness.processElement(createTestOrder("BUY1", contractCde, "B", 100.0, 1000L, 1000L), 1000L);
        testHarness.processElement(createTestOrder("BUY2", contractCde, "B", 99.0, 800L, 1100L), 1100L);
        testHarness.processElement(createTestOrder("BUY3", contractCde, "B", 98.0, 600L, 1200L), 1200L);
        
        // 添加多个不同价位的卖单
        testHarness.processElement(createTestOrder("SELL1", contractCde, "S", 101.0, 500L, 1300L), 1300L);
        testHarness.processElement(createTestOrder("SELL2", contractCde, "S", 102.0, 700L, 1400L), 1400L);
        testHarness.processElement(createTestOrder("SELL3", contractCde, "S", 103.0, 900L, 1500L), 1500L);
        
        List<BaseOrderBook> orderBooks = testHarness.extractOutputValues();
        BaseOrderBook finalOrderBook = orderBooks.get(orderBooks.size() - 1);
        
        // 验证订单簿深度
        assertEquals(3, finalOrderBook.getBids().size());
        assertEquals(3, finalOrderBook.getAsks().size());
        
        // 验证最优价格
        assertEquals(100.0, finalOrderBook.getBestBid()); // 最高买价
        assertEquals(101.0, finalOrderBook.getBestAsk()); // 最低卖价
        
        // 验证总量
        assertEquals(2400L, finalOrderBook.getTotalBidVolume()); // 1000+800+600
        assertEquals(2100L, finalOrderBook.getTotalAskVolume()); // 500+700+900
    }
    
    @Test
    @DisplayName("测试订单取消（数量为0）")
    void testOrderCancellation() throws Exception {
        String contractCde = "TEST_CONTRACT";
        
        // 添加订单
        SingleLegOrder buyOrder = createTestOrder("ORDER1", contractCde, "B", 100.0, 1000L, 1000L);
        testHarness.processElement(buyOrder, 1000L);
        
        // 取消订单（数量设为0）
        SingleLegOrder cancelOrder = createTestOrder("ORDER1", contractCde, "B", 100.0, 0L, 1500L);
        testHarness.processElement(cancelOrder, 1500L);
        
        List<BaseOrderBook> orderBooks = testHarness.extractOutputValues();
        BaseOrderBook finalOrderBook = orderBooks.get(1);
        
        // 验证订单被移除
        assertTrue(finalOrderBook.getBids().isEmpty());
        assertEquals(0.0, finalOrderBook.getBestBid());
    }
    
    @Test
    @DisplayName("测试订单部分成交")
    void testPartialOrderExecution() throws Exception {
        String contractCde = "TEST_CONTRACT";
        
        // 添加原始订单
        SingleLegOrder originalOrder = createTestOrder("ORDER1", contractCde, "B", 100.0, 1000L, 1000L);
        testHarness.processElement(originalOrder, 1000L);
        
        // 部分成交后的订单更新
        SingleLegOrder updatedOrder = createTestOrder("ORDER1", contractCde, "B", 100.0, 600L, 1500L);
        testHarness.processElement(updatedOrder, 1500L);
        
        List<BaseOrderBook> orderBooks = testHarness.extractOutputValues();
        BaseOrderBook finalOrderBook = orderBooks.get(1);
        
        // 验证剩余数量
        assertEquals(100.0, finalOrderBook.getBestBid());
        assertEquals(600L, finalOrderBook.getBestBidVolume());
    }
    
    @Test
    @DisplayName("测试无效订单处理")
    void testInvalidOrderHandling() throws Exception {
        String contractCde = "TEST_CONTRACT";
        
        // 测试空订单号
        SingleLegOrder invalidOrder1 = createTestOrder("", contractCde, "B", 100.0, 1000L, 1000L);
        testHarness.processElement(invalidOrder1, 1000L);
        
        // 测试无效买卖标志
        SingleLegOrder invalidOrder2 = createTestOrder("ORDER2", contractCde, "X", 100.0, 1000L, 1100L);
        testHarness.processElement(invalidOrder2, 1100L);
        
        // 测试负价格
        SingleLegOrder invalidOrder3 = createTestOrder("ORDER3", contractCde, "B", -100.0, 1000L, 1200L);
        testHarness.processElement(invalidOrder3, 1200L);
        
        List<BaseOrderBook> orderBooks = testHarness.extractOutputValues();
        
        // 无效订单应该被忽略，不产生订单簿输出
        assertTrue(orderBooks.isEmpty() || orderBooks.stream().allMatch(book -> book.isEmpty()));
    }
    
    @Test
    @DisplayName("测试事件时间定时器（订单过期）")
    void testEventTimeTimerForOrderExpiry() throws Exception {
        String contractCde = "TEST_CONTRACT";
        long currentTime = 1000L;
        long expiryTime = 2000L;
        
        // 添加带过期时间的订单
        SingleLegOrder orderWithExpiry = createTestOrder("ORDER1", contractCde, "B", 100.0, 1000L, currentTime);
        orderWithExpiry.setExpiryTimestamp(expiryTime);
        
        testHarness.processElement(orderWithExpiry, currentTime);
        
        // 推进事件时间到过期时间
        testHarness.processWatermark(expiryTime);
        
        List<BaseOrderBook> orderBooks = testHarness.extractOutputValues();
        
        // 应该有两个订单簿输出：添加订单时一个，过期清理时一个
        assertTrue(orderBooks.size() >= 1);
        
        // 验证最终订单簿为空（订单已过期）
        if (orderBooks.size() > 1) {
            BaseOrderBook finalOrderBook = orderBooks.get(orderBooks.size() - 1);
            assertTrue(finalOrderBook.isEmpty() || finalOrderBook.getBestBidVolume() == 0);
        }
    }
    
    /**
     * 创建测试用的SingleLegOrder对象
     */
    private SingleLegOrder createTestOrder(String ordNbr, String contractCde, String bsTag, 
                                         double price, long volume, long timestamp) {
        SingleLegOrder order = new SingleLegOrder();
        order.setOrdNbr(ordNbr);
        order.setContractCde(contractCde);
        order.setBSTag(bsTag);
        order.setTrdPrc(price);
        order.setRmnVol(volume);
        order.setTimestamp(timestamp);
        return order;
    }
}
