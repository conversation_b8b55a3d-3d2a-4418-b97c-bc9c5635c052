package com.futures.watermark;

import com.futures.model.SingleLegOrder;
import org.apache.flink.api.common.eventtime.Watermark;
import org.apache.flink.api.common.eventtime.WatermarkOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.time.Duration;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * IdleAwareWatermarkGenerator 单元测试
 * 
 * 测试空闲检测水印生成器的核心功能：
 * 1. 正常水印生成
 * 2. 空闲检测和水印推进
 * 3. 时间戳跟踪
 * 4. 统计信息
 * 
 * <AUTHOR> System Team
 */
class IdleAwareWatermarkGeneratorTest {
    
    @Mock
    private WatermarkOutput watermarkOutput;
    
    private IdleAwareWatermarkGenerator<SingleLegOrder> watermarkGenerator;
    
    private static final Duration MAX_OUT_OF_ORDERNESS = Duration.ofMillis(500);
    private static final Duration IDLE_TIMEOUT = Duration.ofSeconds(2);
    
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        watermarkGenerator = new IdleAwareWatermarkGenerator<>(MAX_OUT_OF_ORDERNESS, IDLE_TIMEOUT);
    }
    
    @Test
    @DisplayName("测试初始状态")
    void testInitialState() {
        assertEquals(0L, watermarkGenerator.getEventCount());
        assertEquals(0L, watermarkGenerator.getWatermarkCount());
        assertFalse(watermarkGenerator.isIdle());
        assertEquals(Long.MIN_VALUE, watermarkGenerator.getLastEmittedWatermark());
    }
    
    @Test
    @DisplayName("测试事件处理和时间戳更新")
    void testEventProcessingAndTimestampUpdate() {
        SingleLegOrder order1 = createTestOrder("ORDER1", 1000L);
        SingleLegOrder order2 = createTestOrder("ORDER2", 1500L);
        SingleLegOrder order3 = createTestOrder("ORDER3", 1200L); // 乱序事件
        
        // 处理第一个事件
        watermarkGenerator.onEvent(order1, 1000L, watermarkOutput);
        assertEquals(1L, watermarkGenerator.getEventCount());
        assertEquals(1000L, watermarkGenerator.getMaxTimestamp());
        assertFalse(watermarkGenerator.isIdle());
        
        // 处理第二个事件（更新最大时间戳）
        watermarkGenerator.onEvent(order2, 1500L, watermarkOutput);
        assertEquals(2L, watermarkGenerator.getEventCount());
        assertEquals(1500L, watermarkGenerator.getMaxTimestamp());
        
        // 处理乱序事件（不应更新最大时间戳）
        watermarkGenerator.onEvent(order3, 1200L, watermarkOutput);
        assertEquals(3L, watermarkGenerator.getEventCount());
        assertEquals(1500L, watermarkGenerator.getMaxTimestamp()); // 保持不变
    }
    
    @Test
    @DisplayName("测试正常水印生成")
    void testNormalWatermarkGeneration() {
        SingleLegOrder order = createTestOrder("ORDER1", 1000L);
        watermarkGenerator.onEvent(order, 1000L, watermarkOutput);
        
        // 触发周期性水印生成
        watermarkGenerator.onPeriodicEmit(watermarkOutput);
        
        // 验证水印生成
        ArgumentCaptor<Watermark> watermarkCaptor = ArgumentCaptor.forClass(Watermark.class);
        verify(watermarkOutput).emitWatermark(watermarkCaptor.capture());
        
        Watermark emittedWatermark = watermarkCaptor.getValue();
        long expectedWatermark = 1000L - MAX_OUT_OF_ORDERNESS.toMillis();
        assertEquals(expectedWatermark, emittedWatermark.getTimestamp());
        
        assertEquals(1L, watermarkGenerator.getWatermarkCount());
        assertEquals(expectedWatermark, watermarkGenerator.getLastEmittedWatermark());
    }
    
    @Test
    @DisplayName("测试空闲检测和水印推进")
    void testIdleDetectionAndWatermarkAdvancement() throws InterruptedException {
        SingleLegOrder order = createTestOrder("ORDER1", 1000L);
        watermarkGenerator.onEvent(order, 1000L, watermarkOutput);
        
        // 等待超过空闲超时时间
        Thread.sleep(IDLE_TIMEOUT.toMillis() + 100);
        
        // 触发周期性水印生成
        watermarkGenerator.onPeriodicEmit(watermarkOutput);
        
        // 验证进入空闲状态并推进水印到最大时间戳
        assertTrue(watermarkGenerator.isIdle());
        
        ArgumentCaptor<Watermark> watermarkCaptor = ArgumentCaptor.forClass(Watermark.class);
        verify(watermarkOutput).emitWatermark(watermarkCaptor.capture());
        
        Watermark emittedWatermark = watermarkCaptor.getValue();
        assertEquals(1000L, emittedWatermark.getTimestamp()); // 推进到最大时间戳
    }
    
    @Test
    @DisplayName("测试从空闲状态恢复")
    void testRecoveryFromIdleState() throws InterruptedException {
        SingleLegOrder order1 = createTestOrder("ORDER1", 1000L);
        watermarkGenerator.onEvent(order1, 1000L, watermarkOutput);
        
        // 等待进入空闲状态
        Thread.sleep(IDLE_TIMEOUT.toMillis() + 100);
        watermarkGenerator.onPeriodicEmit(watermarkOutput);
        assertTrue(watermarkGenerator.isIdle());
        
        // 处理新事件，应该从空闲状态恢复
        SingleLegOrder order2 = createTestOrder("ORDER2", 2000L);
        watermarkGenerator.onEvent(order2, 2000L, watermarkOutput);
        
        assertFalse(watermarkGenerator.isIdle());
        assertEquals(2000L, watermarkGenerator.getMaxTimestamp());
        assertEquals(2L, watermarkGenerator.getEventCount());
    }
    
    @Test
    @DisplayName("测试水印单调递增")
    void testWatermarkMonotonicity() {
        SingleLegOrder order1 = createTestOrder("ORDER1", 1000L);
        SingleLegOrder order2 = createTestOrder("ORDER2", 800L); // 更早的时间戳
        
        watermarkGenerator.onEvent(order1, 1000L, watermarkOutput);
        watermarkGenerator.onPeriodicEmit(watermarkOutput);
        
        long firstWatermark = watermarkGenerator.getLastEmittedWatermark();
        
        // 处理更早的事件
        watermarkGenerator.onEvent(order2, 800L, watermarkOutput);
        watermarkGenerator.onPeriodicEmit(watermarkOutput);
        
        // 水印应该保持单调递增（不应该倒退）
        assertTrue(watermarkGenerator.getLastEmittedWatermark() >= firstWatermark);
    }
    
    @Test
    @DisplayName("测试统计信息重置")
    void testStatsReset() {
        SingleLegOrder order = createTestOrder("ORDER1", 1000L);
        watermarkGenerator.onEvent(order, 1000L, watermarkOutput);
        watermarkGenerator.onPeriodicEmit(watermarkOutput);
        
        assertTrue(watermarkGenerator.getEventCount() > 0);
        assertTrue(watermarkGenerator.getWatermarkCount() > 0);
        
        // 重置统计信息
        watermarkGenerator.resetStats();
        
        assertEquals(0L, watermarkGenerator.getEventCount());
        assertEquals(0L, watermarkGenerator.getWatermarkCount());
        assertFalse(watermarkGenerator.isIdle());
    }
    
    @Test
    @DisplayName("测试多次水印生成不重复发射相同值")
    void testNoDuplicateWatermarkEmission() {
        SingleLegOrder order = createTestOrder("ORDER1", 1000L);
        watermarkGenerator.onEvent(order, 1000L, watermarkOutput);
        
        // 多次触发周期性水印生成
        watermarkGenerator.onPeriodicEmit(watermarkOutput);
        watermarkGenerator.onPeriodicEmit(watermarkOutput);
        watermarkGenerator.onPeriodicEmit(watermarkOutput);
        
        // 应该只发射一次水印（因为时间戳没有变化）
        verify(watermarkOutput, times(1)).emitWatermark(any(Watermark.class));
        assertEquals(1L, watermarkGenerator.getWatermarkCount());
    }
    
    @Test
    @DisplayName("测试极端时间戳值")
    void testExtremeTimestampValues() {
        // 测试最大值
        SingleLegOrder orderMax = createTestOrder("ORDER_MAX", Long.MAX_VALUE - 1000);
        watermarkGenerator.onEvent(orderMax, Long.MAX_VALUE - 1000, watermarkOutput);
        
        assertEquals(Long.MAX_VALUE - 1000, watermarkGenerator.getMaxTimestamp());
        
        // 测试负值（应该被忽略或处理）
        SingleLegOrder orderNegative = createTestOrder("ORDER_NEG", -1000L);
        watermarkGenerator.onEvent(orderNegative, -1000L, watermarkOutput);
        
        // 最大时间戳不应该倒退
        assertEquals(Long.MAX_VALUE - 1000, watermarkGenerator.getMaxTimestamp());
    }
    
    @Test
    @DisplayName("测试toString方法")
    void testToStringMethod() {
        SingleLegOrder order = createTestOrder("ORDER1", 1000L);
        watermarkGenerator.onEvent(order, 1000L, watermarkOutput);
        
        String toString = watermarkGenerator.toString();
        
        assertNotNull(toString);
        assertTrue(toString.contains("IdleAwareWatermarkGenerator"));
        assertTrue(toString.contains("maxTimestamp=1000"));
        assertTrue(toString.contains("eventCount=1"));
    }
    
    /**
     * 创建测试用的SingleLegOrder对象
     */
    private SingleLegOrder createTestOrder(String ordNbr, long timestamp) {
        SingleLegOrder order = new SingleLegOrder();
        order.setOrdNbr(ordNbr);
        order.setContractCde("TEST_CONTRACT");
        order.setBSTag("B");
        order.setTrdPrc(100.0);
        order.setRmnVol(1000L);
        order.setTimestamp(timestamp);
        return order;
    }
}
