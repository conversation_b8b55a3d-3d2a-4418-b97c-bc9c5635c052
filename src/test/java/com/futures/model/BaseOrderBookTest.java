package com.futures.model;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.util.TreeMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * BaseOrderBook 单元测试
 * 
 * 测试基础订单簿数据模型的核心功能：
 * 1. 订单簿构建和初始化
 * 2. 最优价格计算
 * 3. 深度查询
 * 4. 统计信息计算
 * 
 * <AUTHOR> System Team
 */
class BaseOrderBookTest {
    
    private BaseOrderBook orderBook;
    private static final String TEST_INSTRUMENT = "TEST_CONTRACT";
    
    @BeforeEach
    void setUp() {
        orderBook = new BaseOrderBook(TEST_INSTRUMENT);
    }
    
    @Test
    @DisplayName("测试空订单簿初始状态")
    void testEmptyOrderBookInitialState() {
        assertEquals(TEST_INSTRUMENT, orderBook.getInstrumentId());
        assertTrue(orderBook.isEmpty());
        assertFalse(orderBook.isValid());
        assertEquals(0.0, orderBook.getBestBid());
        assertEquals(Double.MAX_VALUE, orderBook.getBestAsk());
        assertEquals(0L, orderBook.getBestBidVolume());
        assertEquals(0L, orderBook.getBestAskVolume());
        assertEquals(0L, orderBook.getTotalBidVolume());
        assertEquals(0L, orderBook.getTotalAskVolume());
        assertEquals(0L, orderBook.getVersion());
    }
    
    @Test
    @DisplayName("测试买价订单簿构建")
    void testBidOrderBookBuilding() {
        TreeMap<Double, Long> bids = new TreeMap<>(java.util.Collections.reverseOrder());
        bids.put(100.0, 1000L);
        bids.put(99.0, 800L);
        bids.put(98.0, 600L);
        
        orderBook.setBids(bids);
        
        assertFalse(orderBook.isEmpty());
        assertEquals(100.0, orderBook.getBestBid());
        assertEquals(1000L, orderBook.getBestBidVolume());
        assertEquals(2400L, orderBook.getTotalBidVolume());
        assertEquals(3, orderBook.getBids().size());
        
        // 验证买价按降序排列
        Double[] bidPrices = orderBook.getBids().keySet().toArray(new Double[0]);
        assertTrue(bidPrices[0] > bidPrices[1]);
        assertTrue(bidPrices[1] > bidPrices[2]);
    }
    
    @Test
    @DisplayName("测试卖价订单簿构建")
    void testAskOrderBookBuilding() {
        TreeMap<Double, Long> asks = new TreeMap<>();
        asks.put(101.0, 500L);
        asks.put(102.0, 700L);
        asks.put(103.0, 900L);
        
        orderBook.setAsks(asks);
        
        assertFalse(orderBook.isEmpty());
        assertEquals(101.0, orderBook.getBestAsk());
        assertEquals(500L, orderBook.getBestAskVolume());
        assertEquals(2100L, orderBook.getTotalAskVolume());
        assertEquals(3, orderBook.getAsks().size());
        
        // 验证卖价按升序排列
        Double[] askPrices = orderBook.getAsks().keySet().toArray(new Double[0]);
        assertTrue(askPrices[0] < askPrices[1]);
        assertTrue(askPrices[1] < askPrices[2]);
    }
    
    @Test
    @DisplayName("测试完整订单簿构建")
    void testCompleteOrderBookBuilding() {
        TreeMap<Double, Long> bids = new TreeMap<>(java.util.Collections.reverseOrder());
        bids.put(100.0, 1000L);
        bids.put(99.0, 800L);
        
        TreeMap<Double, Long> asks = new TreeMap<>();
        asks.put(101.0, 500L);
        asks.put(102.0, 700L);
        
        orderBook.setBids(bids);
        orderBook.setAsks(asks);
        
        assertFalse(orderBook.isEmpty());
        assertTrue(orderBook.isValid());
        
        assertEquals(100.0, orderBook.getBestBid());
        assertEquals(101.0, orderBook.getBestAsk());
        assertEquals(1.0, orderBook.getSpread());
        assertEquals(100.5, orderBook.getMidPrice());
        
        assertEquals(1800L, orderBook.getTotalBidVolume());
        assertEquals(1200L, orderBook.getTotalAskVolume());
    }
    
    @Test
    @DisplayName("测试价差计算")
    void testSpreadCalculation() {
        TreeMap<Double, Long> bids = new TreeMap<>(java.util.Collections.reverseOrder());
        TreeMap<Double, Long> asks = new TreeMap<>();
        
        // 正常价差
        bids.put(100.0, 1000L);
        asks.put(105.0, 500L);
        orderBook.setBids(bids);
        orderBook.setAsks(asks);
        assertEquals(5.0, orderBook.getSpread());
        assertEquals(102.5, orderBook.getMidPrice());
        
        // 零价差（买卖价相等）
        asks.clear();
        asks.put(100.0, 500L);
        orderBook.setAsks(asks);
        assertEquals(0.0, orderBook.getSpread());
        assertEquals(100.0, orderBook.getMidPrice());
        
        // 只有买价
        asks.clear();
        orderBook.setAsks(asks);
        assertEquals(Double.MAX_VALUE, orderBook.getSpread());
        assertEquals(0.0, orderBook.getMidPrice());
        
        // 只有卖价
        bids.clear();
        orderBook.setBids(bids);
        asks.put(105.0, 500L);
        orderBook.setAsks(asks);
        assertEquals(Double.MAX_VALUE, orderBook.getSpread());
        assertEquals(0.0, orderBook.getMidPrice());
    }
    
    @Test
    @DisplayName("测试指定深度查询")
    void testDepthQuery() {
        TreeMap<Double, Long> bids = new TreeMap<>(java.util.Collections.reverseOrder());
        bids.put(100.0, 1000L);
        bids.put(99.0, 800L);
        bids.put(98.0, 600L);
        bids.put(97.0, 400L);
        bids.put(96.0, 200L);
        
        TreeMap<Double, Long> asks = new TreeMap<>();
        asks.put(101.0, 500L);
        asks.put(102.0, 700L);
        asks.put(103.0, 900L);
        asks.put(104.0, 300L);
        asks.put(105.0, 100L);
        
        orderBook.setBids(bids);
        orderBook.setAsks(asks);
        
        // 测试买价深度查询
        Map<Double, Long> topBids = orderBook.getBidsAtDepth(3);
        assertEquals(3, topBids.size());
        assertTrue(topBids.containsKey(100.0));
        assertTrue(topBids.containsKey(99.0));
        assertTrue(topBids.containsKey(98.0));
        assertFalse(topBids.containsKey(97.0));
        
        // 测试卖价深度查询
        Map<Double, Long> topAsks = orderBook.getAsksAtDepth(2);
        assertEquals(2, topAsks.size());
        assertTrue(topAsks.containsKey(101.0));
        assertTrue(topAsks.containsKey(102.0));
        assertFalse(topAsks.containsKey(103.0));
        
        // 测试超出实际深度的查询
        Map<Double, Long> allBids = orderBook.getBidsAtDepth(10);
        assertEquals(5, allBids.size()); // 实际只有5个价位
    }
    
    @Test
    @DisplayName("测试版本管理")
    void testVersionManagement() {
        assertEquals(0L, orderBook.getVersion());
        
        orderBook.incrementVersion();
        assertEquals(1L, orderBook.getVersion());
        assertTrue(orderBook.getUpdateTimestamp() > 0);
        
        long firstUpdateTime = orderBook.getUpdateTimestamp();
        
        // 等待一小段时间确保时间戳不同
        try {
            Thread.sleep(1);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        orderBook.incrementVersion();
        assertEquals(2L, orderBook.getVersion());
        assertTrue(orderBook.getUpdateTimestamp() > firstUpdateTime);
    }
    
    @Test
    @DisplayName("测试订单簿有效性验证")
    void testOrderBookValidation() {
        // 空订单簿无效
        assertFalse(orderBook.isValid());
        
        // 只有买价的订单簿有效
        TreeMap<Double, Long> bids = new TreeMap<>(java.util.Collections.reverseOrder());
        bids.put(100.0, 1000L);
        orderBook.setBids(bids);
        assertTrue(orderBook.isValid());
        
        // 只有卖价的订单簿有效
        orderBook.setBids(new TreeMap<>(java.util.Collections.reverseOrder()));
        TreeMap<Double, Long> asks = new TreeMap<>();
        asks.put(101.0, 500L);
        orderBook.setAsks(asks);
        assertTrue(orderBook.isValid());
        
        // 买卖价都有且买价<=卖价的订单簿有效
        bids.put(100.0, 1000L);
        orderBook.setBids(bids);
        assertTrue(orderBook.isValid());
        
        // 买价>卖价的订单簿无效（交叉订单簿）
        asks.clear();
        asks.put(99.0, 500L); // 卖价低于买价
        orderBook.setAsks(asks);
        assertFalse(orderBook.isValid());
    }
    
    @Test
    @DisplayName("测试equals和hashCode")
    void testEqualsAndHashCode() {
        BaseOrderBook orderBook1 = new BaseOrderBook("CONTRACT1");
        BaseOrderBook orderBook2 = new BaseOrderBook("CONTRACT1");
        BaseOrderBook orderBook3 = new BaseOrderBook("CONTRACT2");
        
        TreeMap<Double, Long> bids = new TreeMap<>(java.util.Collections.reverseOrder());
        bids.put(100.0, 1000L);
        
        orderBook1.setBids(bids);
        orderBook2.setBids(bids);
        
        assertEquals(orderBook1, orderBook2);
        assertEquals(orderBook1.hashCode(), orderBook2.hashCode());
        
        assertNotEquals(orderBook1, orderBook3);
        assertNotEquals(orderBook1.hashCode(), orderBook3.hashCode());
    }
    
    @Test
    @DisplayName("测试toString方法")
    void testToStringMethod() {
        TreeMap<Double, Long> bids = new TreeMap<>(java.util.Collections.reverseOrder());
        bids.put(100.0, 1000L);
        TreeMap<Double, Long> asks = new TreeMap<>();
        asks.put(101.0, 500L);
        
        orderBook.setBids(bids);
        orderBook.setAsks(asks);
        orderBook.incrementVersion();
        
        String toString = orderBook.toString();
        
        assertNotNull(toString);
        assertTrue(toString.contains("BaseOrderBook"));
        assertTrue(toString.contains(TEST_INSTRUMENT));
        assertTrue(toString.contains("bestBid=100.0"));
        assertTrue(toString.contains("bestAsk=101.0"));
        assertTrue(toString.contains("spread=1.0"));
        assertTrue(toString.contains("version=1"));
    }
}
