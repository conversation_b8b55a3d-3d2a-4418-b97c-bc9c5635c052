package com.futures.serialization;

import com.futures.model.SingleLegOrder;
import com.futures.model.ComboOrder;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 反序列化测试
 * 
 * 验证JSON字段映射是否正确
 * 
 * <AUTHOR> System Team
 */
class DeserializationTest {
    
    @Test
    @DisplayName("测试单腿订单JSON反序列化")
    void testSingleLegOrderDeserialization() throws Exception {
        String jsonData = """
            {
                "_seq": 321166529,
                "_eno": 262173829,
                "trd_dt": "2025-03-17",
                "ord_nbr": "10000000",
                "memb_cde": "0049",
                "trd_cde": "30046648",
                "seat_nbr": "569038429560052",
                "contract_cde": "JD2504-C-3200",
                "ord_prc_cndt": "2",
                "b_s_tag": "B",
                "ocpos_type": "0",
                "specu_hedg_tag": "S",
                "ord_prc": 4.0,
                "ord_vol": 5.0,
                "vldprd_type": "2",
                "trd_vol_type": "3",
                "min_trd_vol": 0.0,
                "trig_cndt": "1"
            }
            """;
        
        SingleLegOrderDeserializer deserializer = new SingleLegOrderDeserializer();
        deserializer.open(null);
        
        SingleLegOrder order = deserializer.deserialize(jsonData.getBytes());
        
        assertNotNull(order);
        assertEquals("10000000", order.getOrdNbr());
        assertEquals("JD2504-C-3200", order.getContractCde());
        assertEquals("B", order.getBSTag());
        assertEquals(4.0, order.getTrdPrc(), 0.001);
        assertEquals(5L, order.getRmnVol());
        assertTrue(order.isValid());
        assertTrue(order.isBuyOrder());
        
        System.out.println("✅ 单腿订单反序列化成功: " + order);
    }
    
    @Test
    @DisplayName("测试组合订单JSON反序列化")
    void testComboOrderDeserialization() throws Exception {
        String jsonData = """
            {
                "_seq": 130649837,
                "_eno": 490084,
                "trd_dt": "2025-03-17",
                "ord_nbr": "100257414",
                "memb_cde": "0160",
                "trd_cde": "4651284",
                "seat_nbr": "01602004",
                "contract_cde": "SPD_EB2504_EB2505",
                "ord_prc_cndt": "2",
                "b_s_tag": "B",
                "ocpos_type": "0",
                "specu_hedg_tag": "S",
                "ord_prc": 58.0,
                "ord_vol": 13.0,
                "vldprd_type": "3",
                "trd_vol_type": "3",
                "min_trd_vol": 0.0,
                "trig_cndt": "1"
            }
            """;
        
        ComboOrderDeserializer deserializer = new ComboOrderDeserializer();
        deserializer.open(null);
        
        ComboOrder order = deserializer.deserialize(jsonData.getBytes());
        
        assertNotNull(order);
        assertEquals("100257414", order.getOrdNbr());
        assertEquals("SPD_EB2504_EB2505", order.getContractCde());
        assertEquals("B", order.getBSTag());
        assertEquals(58.0, order.getTrdPrc(), 0.001);
        assertEquals(13L, order.getRmnVol());
        assertTrue(order.isValid());
        assertTrue(order.isBuyOrder());
        
        // 测试组合信息解析
        order.parseComboFromContractCde();
        assertEquals("EB2504", order.getLeg1ContractCde());
        assertEquals("EB2505", order.getLeg2ContractCde());
        assertEquals("EB2504-EB2505", order.getComboId());
        
        System.out.println("✅ 组合订单反序列化成功: " + order);
    }
    
    @Test
    @DisplayName("测试零数量订单处理")
    void testZeroVolumeOrderHandling() throws Exception {
        String jsonData = """
            {
                "ord_nbr": "100257466",
                "contract_cde": "EB2505",
                "b_s_tag": "S",
                "ord_prc": 100.0,
                "ord_vol": 0.0
            }
            """;
        
        SingleLegOrderDeserializer deserializer = new SingleLegOrderDeserializer();
        deserializer.open(null);
        
        SingleLegOrder order = deserializer.deserialize(jsonData.getBytes());
        
        assertNotNull(order);
        assertEquals("100257466", order.getOrdNbr());
        assertEquals(100.0, order.getTrdPrc(), 0.001);
        assertEquals(0L, order.getRmnVol());
        assertFalse(order.isValid()); // 数量为0应该无效
        
        System.out.println("✅ 零数量订单处理正确: " + order);
    }
}
