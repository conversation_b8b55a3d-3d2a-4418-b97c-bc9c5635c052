#!/bin/bash

# 期货订单簿系统修复版启动脚本
# Futures Order Book System Fixed Startup Script

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 启动期货订单簿系统（修复版）${NC}"
echo -e "${BLUE}📋 修复内容：${NC}"
echo -e "   ✅ 修复Java模块系统兼容性问题"
echo -e "   ✅ 禁用对象重用避免序列化问题"
echo -e "   ✅ 从latest位置开始消费，避免重复处理"
echo -e "   ✅ 减少过度的调试日志"
echo -e "${YELLOW}📁 业务日志: logs/futures-orderbook-business.log${NC}"
echo ""

cd "$PROJECT_ROOT"

# 清理之前的日志
if [ -d "logs" ]; then
    rm -f logs/futures-orderbook*.log
    echo "🧹 已清理旧日志文件"
fi

# 设置JVM参数，修复Java模块系统问题
export MAVEN_OPTS="-Xms512m -Xmx2048m -XX:+UseG1GC -XX:MaxGCPauseMillis=200 \
--add-opens java.base/java.util=ALL-UNNAMED \
--add-opens java.base/java.lang=ALL-UNNAMED \
--add-opens java.base/java.lang.reflect=ALL-UNNAMED \
--add-opens java.base/java.nio=ALL-UNNAMED \
--add-opens java.base/sun.nio.ch=ALL-UNNAMED"

echo "🔧 JVM参数已配置，修复模块系统问题"

# 编译项目
echo "📦 编译项目..."
mvn compile -q

if [ $? -ne 0 ]; then
    echo -e "${RED}❌ 编译失败${NC}"
    exit 1
fi

echo "✅ 编译成功"

# 启动作业，使用内存状态后端避免RocksDB问题
echo "🚀 启动作业..."
mvn exec:java \
    -Dexec.mainClass="com.futures.job.FuturesOrderBookKafkaJob" \
    -Dexec.args="--state.backend.type=memory --kafka.bootstrap.servers=localhost:9092 --parallelism=1" \
    -q

echo -e "${GREEN}✅ 作业已启动${NC}"
