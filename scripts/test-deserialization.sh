#!/bin/bash

# 测试反序列化修复效果
# Test Deserialization Fix

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo "🧪 测试反序列化修复效果..."
echo ""

cd "$PROJECT_ROOT"

# 运行反序列化测试
echo "📋 运行反序列化单元测试..."
mvn test -Dtest=DeserializationTest -q

if [ $? -eq 0 ]; then
    echo "✅ 反序列化测试通过"
else
    echo "❌ 反序列化测试失败"
    exit 1
fi

echo ""
echo "🎯 修复总结:"
echo "1. ✅ 修正了JSON字段映射: ord_prc -> trd_prc, ord_vol -> rmn_vol, b_s_tag -> bSTag"
echo "2. ✅ 改进了订单处理日志，区分新增/更新/取消操作"
echo "3. ✅ 增强了订单簿构建调试信息"
echo "4. ✅ 添加了BBO输出日志"
echo ""
echo "🚀 现在可以重新运行系统，应该能看到正确的价格和数量了！"
