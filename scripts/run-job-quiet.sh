#!/bin/bash

# 期货订单簿系统静默启动脚本（只显示业务日志）
# Futures Order Book System Quiet Startup Script

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 启动期货订单簿系统（静默模式）${NC}"
echo -e "${BLUE}📋 只显示订单簿相关的业务日志${NC}"
echo -e "${YELLOW}📁 完整日志请查看: logs/futures-orderbook-business.log${NC}"
echo ""

cd "$PROJECT_ROOT"

# 设置JVM参数，减少不必要的日志，并修复Java模块系统问题
export MAVEN_OPTS="-Xms512m -Xmx2048m -XX:+UseG1GC -XX:MaxGCPauseMillis=200 --add-opens java.base/java.util=ALL-UNNAMED --add-opens java.base/java.lang=ALL-UNNAMED --add-opens java.base/java.lang.reflect=ALL-UNNAMED"

# 启动作业，使用内存状态后端避免RocksDB问题
mvn exec:java \
    -Dexec.mainClass="com.futures.job.FuturesOrderBookKafkaJob" \
    -Dexec.args="--state.backend.type=memory --kafka.bootstrap.servers=localhost:9092" \
    -q

echo -e "${GREEN}✅ 作业已启动${NC}"
