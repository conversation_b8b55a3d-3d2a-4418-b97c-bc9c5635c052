#!/bin/bash

# 期货订单簿系统启动脚本
# Futures Order Book System Startup Script

set -e

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
LOG_DIR="$PROJECT_ROOT/logs"
PID_FILE="$PROJECT_ROOT/futures-orderbook.pid"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_debug() {
    if [[ "$DEBUG" == "true" ]]; then
        echo -e "${BLUE}[DEBUG]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
    fi
}

# 显示帮助信息
show_help() {
    cat << EOF
期货订单簿系统启动脚本

用法: $0 [选项]

选项:
    -h, --help              显示此帮助信息
    -m, --mode MODE         运行模式: local|cluster|standalone (默认: local)
    -k, --kafka SERVERS     Kafka服务器地址 (默认: localhost:9092)
    -g, --group GROUP       Kafka消费者组 (默认: futures-orderbook-consumer)
    -p, --parallelism NUM   并行度 (默认: 4)
    -c, --checkpoint MS     检查点间隔毫秒数 (默认: 30000)
    -w, --watermark MS      最大乱序容忍度毫秒数 (默认: 500)
    -i, --idle MS           空闲超时毫秒数 (默认: 10000)
    -b, --backend TYPE      状态后端类型: memory|rocksdb (默认: memory)
    -d, --debug             启用调试模式
    -s, --stop              停止运行中的作业
    --clean                 清理日志和临时文件
    --build                 重新编译项目

示例:
    $0                                          # 使用默认配置启动
    $0 -m cluster -k prod-kafka:9092 -p 8      # 集群模式启动
    $0 -d -w 100 -i 5000 -b memory             # 调试模式，低延迟配置，内存状态后端
    $0 -b rocksdb                               # 使用RocksDB状态后端
    $0 --stop                                   # 停止作业
    $0 --clean                                  # 清理文件

EOF
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    # 检查Java
    if ! command -v java &> /dev/null; then
        log_error "Java未安装或不在PATH中"
        exit 1
    fi
    
    local java_version=$(java -version 2>&1 | head -n1 | cut -d'"' -f2 | cut -d'.' -f1-2)
    log_debug "Java版本: $java_version"
    
    # 检查Maven
    if ! command -v mvn &> /dev/null; then
        log_error "Maven未安装或不在PATH中"
        exit 1
    fi
    
    # 检查项目文件
    if [[ ! -f "$PROJECT_ROOT/pom.xml" ]]; then
        log_error "未找到pom.xml文件，请确认在正确的项目目录中运行"
        exit 1
    fi
    
    log_info "依赖检查完成"
}

# 创建必要目录
create_directories() {
    log_info "创建必要目录..."
    mkdir -p "$LOG_DIR"
    mkdir -p "$PROJECT_ROOT/tmp"
    mkdir -p "$PROJECT_ROOT/checkpoints"
    mkdir -p "$PROJECT_ROOT/savepoints"
}

# 编译项目
build_project() {
    log_info "编译项目..."
    cd "$PROJECT_ROOT"
    
    if [[ "$DEBUG" == "true" ]]; then
        mvn clean package -DskipTests -X
    else
        mvn clean package -DskipTests -q
    fi
    
    if [[ $? -ne 0 ]]; then
        log_error "项目编译失败"
        exit 1
    fi
    
    log_info "项目编译完成"
}

# 检查Kafka连接
check_kafka() {
    local kafka_servers="$1"
    log_info "检查Kafka连接: $kafka_servers"
    
    # 这里可以添加Kafka连接检查逻辑
    # 例如使用kafka-topics.sh或者telnet检查端口
    
    log_info "Kafka连接检查完成"
}

# 停止运行中的作业
stop_job() {
    log_info "停止运行中的作业..."
    
    if [[ -f "$PID_FILE" ]]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            log_info "发现运行中的作业 (PID: $pid)，正在停止..."
            kill -TERM "$pid"
            
            # 等待进程优雅退出
            local count=0
            while ps -p "$pid" > /dev/null 2>&1 && [[ $count -lt 30 ]]; do
                sleep 1
                ((count++))
            done
            
            if ps -p "$pid" > /dev/null 2>&1; then
                log_warn "进程未能优雅退出，强制终止..."
                kill -KILL "$pid"
            fi
            
            log_info "作业已停止"
        else
            log_warn "PID文件存在但进程不存在，清理PID文件"
        fi
        rm -f "$PID_FILE"
    else
        log_info "未发现运行中的作业"
    fi
}

# 清理文件
clean_files() {
    log_info "清理日志和临时文件..."
    
    # 停止作业
    stop_job
    
    # 清理日志
    if [[ -d "$LOG_DIR" ]]; then
        rm -rf "$LOG_DIR"/*
        log_info "已清理日志文件"
    fi
    
    # 清理临时文件
    if [[ -d "$PROJECT_ROOT/tmp" ]]; then
        rm -rf "$PROJECT_ROOT/tmp"/*
        log_info "已清理临时文件"
    fi
    
    # 清理检查点
    if [[ -d "$PROJECT_ROOT/checkpoints" ]]; then
        rm -rf "$PROJECT_ROOT/checkpoints"/*
        log_info "已清理检查点文件"
    fi
    
    log_info "清理完成"
}

# 启动作业
start_job() {
    local mode="$1"
    local kafka_servers="$2"
    local consumer_group="$3"
    local parallelism="$4"
    local checkpoint_interval="$5"
    local max_out_of_orderness="$6"
    local idle_timeout="$7"
    local state_backend="$8"
    
    log_info "启动期货订单簿作业..."
    log_info "配置参数:"
    log_info "  运行模式: $mode"
    log_info "  Kafka服务器: $kafka_servers"
    log_info "  消费者组: $consumer_group"
    log_info "  并行度: $parallelism"
    log_info "  检查点间隔: ${checkpoint_interval}ms"
    log_info "  最大乱序容忍度: ${max_out_of_orderness}ms"
    log_info "  空闲超时: ${idle_timeout}ms"
    log_info "  状态后端: $state_backend"
    
    cd "$PROJECT_ROOT"
    
    # 构建JVM参数
    local jvm_args="-Xms512m -Xmx2048m -XX:+UseG1GC -XX:MaxGCPauseMillis=200"
    if [[ "$DEBUG" == "true" ]]; then
        jvm_args="$jvm_args -Dlog4j2.level=DEBUG"
    fi
    
    # 构建应用参数
    local app_args=""
    app_args="$app_args --kafka.bootstrap.servers=$kafka_servers"
    app_args="$app_args --kafka.consumer.group=$consumer_group"
    app_args="$app_args --parallelism=$parallelism"
    app_args="$app_args --checkpoint.interval.ms=$checkpoint_interval"
    app_args="$app_args --watermark.max.out.of.orderness.ms=$max_out_of_orderness"
    app_args="$app_args --watermark.idle.timeout.ms=$idle_timeout"
    app_args="$app_args --state.backend.type=$state_backend"
    
    case "$mode" in
        "local")
            log_info "以本地模式启动..."
            export MAVEN_OPTS="$jvm_args"
            mvn exec:java -Dexec.mainClass="com.futures.job.FuturesOrderBookKafkaJob" \
                         -Dexec.args="$app_args" &
            local job_pid=$!
            ;;
        "standalone")
            log_info "以独立模式启动..."
            java $jvm_args -cp "target/futures-orderbook-rebuild-1.0.0.jar:target/lib/*" \
                 com.futures.job.FuturesOrderBookKafkaJob $app_args &
            local job_pid=$!
            ;;
        "cluster")
            log_info "以集群模式启动..."
            if [[ -z "$FLINK_HOME" ]]; then
                log_error "FLINK_HOME环境变量未设置"
                exit 1
            fi
            
            $FLINK_HOME/bin/flink run \
                --parallelism $parallelism \
                target/futures-orderbook-rebuild-1.0.0.jar $app_args
            return $?
            ;;
        *)
            log_error "不支持的运行模式: $mode"
            exit 1
            ;;
    esac
    
    if [[ "$mode" != "cluster" ]]; then
        echo $job_pid > "$PID_FILE"
        log_info "作业已启动 (PID: $job_pid)"
        log_info "日志文件: $LOG_DIR/futures-orderbook.log"
        log_info "使用 '$0 --stop' 停止作业"
        
        # 等待一段时间检查进程是否正常启动
        sleep 3
        if ! ps -p $job_pid > /dev/null 2>&1; then
            log_error "作业启动失败，请检查日志文件"
            rm -f "$PID_FILE"
            exit 1
        fi
        
        log_info "作业启动成功"
    fi
}

# 主函数
main() {
    # 默认参数
    local mode="local"
    local kafka_servers="localhost:9092"
    local consumer_group="futures-orderbook-consumer"
    local parallelism="4"
    local checkpoint_interval="30000"
    local max_out_of_orderness="500"
    local idle_timeout="10000"
    local state_backend="memory"
    local should_build="false"
    local should_stop="false"
    local should_clean="false"
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -m|--mode)
                mode="$2"
                shift 2
                ;;
            -k|--kafka)
                kafka_servers="$2"
                shift 2
                ;;
            -g|--group)
                consumer_group="$2"
                shift 2
                ;;
            -p|--parallelism)
                parallelism="$2"
                shift 2
                ;;
            -c|--checkpoint)
                checkpoint_interval="$2"
                shift 2
                ;;
            -w|--watermark)
                max_out_of_orderness="$2"
                shift 2
                ;;
            -i|--idle)
                idle_timeout="$2"
                shift 2
                ;;
            -b|--backend)
                state_backend="$2"
                shift 2
                ;;
            -d|--debug)
                DEBUG="true"
                shift
                ;;
            -s|--stop)
                should_stop="true"
                shift
                ;;
            --clean)
                should_clean="true"
                shift
                ;;
            --build)
                should_build="true"
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 执行操作
    if [[ "$should_clean" == "true" ]]; then
        clean_files
        exit 0
    fi
    
    if [[ "$should_stop" == "true" ]]; then
        stop_job
        exit 0
    fi
    
    # 检查依赖和准备环境
    check_dependencies
    create_directories
    
    if [[ "$should_build" == "true" ]] || [[ ! -f "$PROJECT_ROOT/target/futures-orderbook-rebuild-1.0.0.jar" ]]; then
        build_project
    fi
    
    # 检查Kafka连接
    check_kafka "$kafka_servers"
    
    # 停止已运行的作业
    stop_job
    
    # 启动新作业
    start_job "$mode" "$kafka_servers" "$consumer_group" "$parallelism" \
              "$checkpoint_interval" "$max_out_of_orderness" "$idle_timeout" "$state_backend"
}

# 信号处理
trap 'log_info "收到中断信号，正在停止..."; stop_job; exit 0' INT TERM

# 执行主函数
main "$@"
