#!/bin/bash

# 测试订单簿修复效果
# Test Order Book Fix

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo "🧪 测试订单簿修复效果..."
echo ""

cd "$PROJECT_ROOT"

# 编译项目
echo "📦 编译项目..."
mvn compile -q

if [ $? -ne 0 ]; then
    echo "❌ 编译失败"
    exit 1
fi

echo "✅ 编译成功"
echo ""

echo "🎯 修复总结:"
echo "1. ✅ 修正了订单状态变化检测，减少重复日志"
echo "2. ✅ 改进了订单簿输出逻辑，区分完全为空和部分为空"
echo "3. ✅ 修复了BBO提取逻辑，即使只有买价或卖价也能输出"
echo "4. ✅ 添加了详细的订单簿构建调试信息"
echo "5. ✅ 优化了日志频率，减少日志洪水"
echo ""

echo "🔍 预期改进效果:"
echo "- 应该能看到 📈 订单簿更新日志"
echo "- 应该能看到 📡 BBO输出日志"
echo "- 重复日志应该大幅减少"
echo "- 能看到订单在订单簿中的聚合过程"
echo ""

echo "🚀 现在可以重新运行系统测试修复效果："
echo "   ./scripts/run-job-quiet.sh"
echo ""
echo "📋 关键日志标识："
echo "   ➕ 新增订单"
echo "   🔄 更新订单"
echo "   ❌ 取消订单"
echo "   🔧 订单簿构建（包含买卖价层级）"
echo "   📈 订单簿更新（应该能看到）"
echo "   📡 BBO输出（应该能看到）"
