@echo off
REM 期货订单簿系统静默启动脚本（Windows版本）
REM Futures Order Book System Quiet Startup Script for Windows

setlocal

set SCRIPT_DIR=%~dp0
set PROJECT_ROOT=%SCRIPT_DIR%..

echo 🚀 启动期货订单簿系统（静默模式）
echo 📋 只显示订单簿相关的业务日志
echo 📁 完整日志请查看: logs\futures-orderbook-business.log
echo.

cd /d "%PROJECT_ROOT%"

REM 设置JVM参数，减少不必要的日志
set MAVEN_OPTS=-Xms512m -Xmx2048m -XX:+UseG1GC -XX:MaxGCPauseMillis=200

REM 启动作业，使用内存状态后端避免RocksDB问题
mvn exec:java ^
    -Dexec.mainClass="com.futures.job.FuturesOrderBookKafkaJob" ^
    -Dexec.args="--state.backend.type=memory --kafka.bootstrap.servers=localhost:9092" ^
    -q

echo ✅ 作业已启动

pause
