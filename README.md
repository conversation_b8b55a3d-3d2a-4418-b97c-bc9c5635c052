# 期货全量订单簿重建系统

基于 Apache Flink 的期货全量订单簿实时重建系统，采用事件时间处理机制和自定义空闲检测水印生成器，确保在高频交易环境下的数据准确性和系统活性。

## 🎯 项目特性

### 核心技术特性
- **事件时间处理**: 基于事件原始时间戳，确保业务逻辑的确定性和可重现性
- **自定义空闲检测水印生成器**: 智能处理分区空闲，防止全局时间停滞
- **分层架构设计**: 基础层处理单腿委托，虚拟层处理组合委托
- **广播状态模式**: BBO信息在基础层和虚拟层之间高效传递
- **事件时间定时器**: 精确处理订单过期逻辑

### 业务功能特性
- **全量订单簿重建**: 实时构建完整的买卖价格深度
- **BBO提取**: 自动提取最优买卖价信息
- **组合合约支持**: 基于腿合约BBO构建虚拟订单簿
- **乱序数据处理**: 容忍网络延迟和数据乱序
- **订单过期管理**: 基于事件时间的订单生命周期管理

## 🏗️ 系统架构

```
[Kafka数据源]
      │
      ▼
[数据解析与标准化]
      │
      ▼
[自定义水印策略应用] ←── IdleAwareWatermarkGenerator
      │
      ├─► [单腿委托流] ──keyBy(contract_cde)──► [基础层订单簿构建器] ──► [基础层订单簿输出]
      │                                              │
      │                                              ▼
      │                                        [BBO广播流]
      │                                              │
      └─► [组合委托流] ──keyBy(combo_id)──────────────┼──► [虚拟层订单簿构建器] ──► [虚拟层订单簿输出]
```

## 🚀 快速开始

### 环境要求
- Java 11+
- Apache Flink 1.20.0
- Apache Kafka 2.8+
- Maven 3.6+

### 1. 编译项目
```bash
mvn clean package -DskipTests
```

### 2. 启动Kafka环境
```bash
# 使用Docker Compose启动Kafka
docker-compose up -d

# 或者手动启动Kafka服务
# 请确保Kafka运行在localhost:9092
```

### 3. 发送测试数据
```bash
# 发送历史数据进行测试
python kafka_producer.py --clear-topics --max-records 1000

# 或者发送模拟实时数据
python kafka_producer.py --mode simulation --simulation-duration 10
```

### 4. 运行Flink作业

#### 方式一：使用Maven运行
```bash
mvn exec:java -Dexec.mainClass="com.futures.job.FuturesOrderBookKafkaJob"
```

#### 方式二：使用Flink命令行
```bash
# 启动Flink集群
$FLINK_HOME/bin/start-cluster.sh

# 提交作业
$FLINK_HOME/bin/flink run target/futures-orderbook-rebuild-1.0.0.jar
```

#### 方式三：使用提供的脚本
```bash
# Linux/Mac - 完整日志模式
./scripts/run-job.sh

# Linux/Mac - 静默模式（只显示业务日志）
./scripts/run-job-quiet.sh

# Windows - 完整日志模式
scripts\run-job.bat

# Windows - 静默模式（只显示业务日志）
scripts\run-job-quiet.bat
```

## 📊 监控和观察

### Web UI访问
- Flink Web UI: http://localhost:8081
- JMX监控端口: 9999

### 日志文件
```
logs/
├── futures-orderbook.log          # 应用主日志
├── futures-orderbook-business.log # 业务日志（订单簿专用）⭐
├── futures-orderbook-error.log    # 错误日志
├── futures-orderbook-debug.log    # 调试日志
└── futures-orderbook-metrics.log  # 性能指标日志
```

**推荐查看业务日志**: `logs/futures-orderbook-business.log` 包含所有订单处理、订单簿更新、BBO变化等关键业务信息。

### 关键指标监控
- 处理延迟: 事件时间 vs 处理时间
- 吞吐量: 每秒处理的订单数
- 水印进度: 各分区的水印推进情况
- 订单簿深度: 各合约的价格层级数量

## ⚙️ 配置说明

### 主要配置参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `kafka.bootstrap.servers` | localhost:9092 | Kafka服务器地址 |
| `watermark.max.out.of.orderness.ms` | 500 | 最大乱序容忍度(毫秒) |
| `watermark.idle.timeout.ms` | 10000 | 空闲超时时间(毫秒) |
| `parallelism` | 4 | 作业并行度 |
| `checkpoint.interval.ms` | 30000 | 检查点间隔(毫秒) |

### 环境特定配置
```bash
# 开发环境
mvn exec:java -Dkafka.bootstrap.servers=localhost:9092

# 生产环境
mvn exec:java -Dkafka.bootstrap.servers=prod-kafka:9092 \
              -Dwatermark.max.out.of.orderness.ms=200 \
              -Dparallelism=8
```

## 🧪 测试

### 运行单元测试
```bash
mvn test
```

### 运行集成测试
```bash
mvn verify
```

### 测试覆盖率报告
```bash
mvn jacoco:report
# 查看 target/site/jacoco/index.html
```

## 📈 性能调优

### 高吞吐量场景
```properties
# 增加并行度
parallelism=16

# 优化检查点
checkpoint.interval.ms=60000
checkpoint.timeout.ms=120000

# 调整水印参数
watermark.max.out.of.orderness.ms=200
watermark.idle.timeout.ms=5000
```

### 低延迟场景
```properties
# 减少水印延迟
watermark.max.out.of.orderness.ms=50
watermark.interval.ms=100

# 优化网络缓冲
network.buffer.memory.fraction=0.2
```

## 🔧 故障排除

### 常见问题

#### 1. 水印不推进
**症状**: 订单簿不更新，定时器不触发
**解决**: 检查空闲检测配置，确认数据流是否正常

#### 2. 内存不足
**症状**: TaskManager频繁重启
**解决**: 增加TaskManager内存或启用RocksDB状态后端

#### 3. Kafka连接失败
**症状**: 无法读取Kafka数据
**解决**: 检查Kafka服务状态和网络连接

### 调试技巧
```bash
# 启用调试日志
export FLINK_LOG_LEVEL=DEBUG

# 查看水印进度
grep "Watermark" logs/futures-orderbook-debug.log

# 监控订单处理统计
grep "Processed.*orders" logs/futures-orderbook.log
```

## 📚 技术文档

### 核心组件说明

#### IdleAwareWatermarkGenerator
自定义水印生成器，解决分区空闲导致的全局水印停滞问题。

**关键特性**:
- 跟踪分区最大事件时间戳
- 检测分区空闲状态
- 智能推进水印至最新位置

#### BaseOrderBookBuilder
基础层订单簿构建器，处理单腿委托数据。

**主要功能**:
- 维护活跃订单状态
- 构建价格深度订单簿
- 提取BBO信息到旁路输出
- 处理订单过期逻辑

#### VirtualOrderBookBuilder
虚拟层订单簿构建器，处理组合委托数据。

**核心能力**:
- 接收BBO广播状态
- 基于腿合约BBO定价
- 构建组合合约虚拟订单簿

### 数据流处理流程

1. **数据接入**: Kafka消费者读取订单数据
2. **时间戳提取**: 提取事件时间戳
3. **水印生成**: 应用自定义水印策略
4. **数据分流**: 按订单类型分为单腿和组合流
5. **基础处理**: 构建基础层订单簿和BBO
6. **虚拟处理**: 结合BBO构建虚拟层订单簿
7. **结果输出**: 输出到下游系统

## 🤝 贡献指南

### 开发环境设置
```bash
git clone <repository-url>
cd flink_futures
mvn clean install
```

### 代码规范
- 遵循Google Java Style Guide
- 使用有意义的变量和方法名
- 添加充分的注释和文档
- 编写单元测试覆盖新功能

### 提交流程
1. Fork项目
2. 创建功能分支
3. 编写代码和测试
4. 提交Pull Request

## 📄 许可证

本项目采用 Apache License 2.0 许可证。

## 📞 联系方式

- 项目维护者: Futures System Team
- 技术支持: [技术支持邮箱]
- 问题反馈: [GitHub Issues]

---

**注意**: 本系统专为期货高频交易场景设计，在生产环境部署前请进行充分的性能测试和压力测试。
